package com.nq.service;

import com.nq.common.ServerResponse;
import com.nq.dao.AgentUserMapper;
import com.nq.pojo.AgentUser;
import com.nq.service.impl.AgentUserBizServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * AgentUserBizService 测试类
 */
public class AgentUserBizServiceTest {

    @Mock
    private AgentUserMapper agentUserMapper;

    @InjectMocks
    private AgentUserBizServiceImpl agentUserBizService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetSubordinateAgents_Success() {
        // 准备测试数据
        Integer agentId = 1;
        
        // 创建测试代理用户
        AgentUser rootAgent = createTestAgent(1, "根代理", 0, 0);
        AgentUser childAgent1 = createTestAgent(2, "子代理1", 1, 1);
        AgentUser childAgent2 = createTestAgent(3, "子代理2", 1, 1);
        AgentUser grandChildAgent = createTestAgent(4, "孙代理", 2, 2);
        
        List<AgentUser> allAgents = Arrays.asList(rootAgent, childAgent1, childAgent2, grandChildAgent);
        
        // 模拟 Mapper 返回
        when(agentUserMapper.selectByPrimaryKey(agentId)).thenReturn(rootAgent);
        when(agentUserMapper.findAll()).thenReturn(allAgents);

        // 执行测试
        ServerResponse<List<AgentUser>> result = agentUserBizService.getSubordinateAgents(agentId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("查询成功", result.getMsg());
        
        List<AgentUser> subordinates = result.getData();
        assertNotNull(subordinates);
        assertEquals(3, subordinates.size()); // 应该包含2个子代理和1个孙代理

        // 验证 Mapper 方法被调用
        verify(agentUserMapper, times(1)).selectByPrimaryKey(agentId);
        verify(agentUserMapper, times(1)).findAll();
    }

    @Test
    public void testGetSubordinateAgents_AgentNotFound() {
        // 准备测试数据
        Integer agentId = 999;

        // 模拟 Mapper 返回 null
        when(agentUserMapper.selectByPrimaryKey(agentId)).thenReturn(null);

        // 执行测试
        ServerResponse<List<AgentUser>> result = agentUserBizService.getSubordinateAgents(agentId);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("代理不存在", result.getMsg());

        // 验证 Mapper 方法被调用
        verify(agentUserMapper, times(1)).selectByPrimaryKey(agentId);
        verify(agentUserMapper, never()).findAll();
    }

    @Test
    public void testGetSubordinateAgents_NullAgentId() {
        // 执行测试
        ServerResponse<List<AgentUser>> result = agentUserBizService.getSubordinateAgents(null);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("代理ID不能为空", result.getMsg());

        // 验证 Mapper 方法未被调用
        verify(agentUserMapper, never()).selectByPrimaryKey(any());
        verify(agentUserMapper, never()).findAll();
    }

    @Test
    public void testGetAgentTreeStructure_Success() {
        // 准备测试数据
        AgentUser rootAgent1 = createTestAgent(1, "根代理1", 0, 0);
        AgentUser rootAgent2 = createTestAgent(2, "根代理2", 0, 0);
        AgentUser childAgent1 = createTestAgent(3, "子代理1", 1, 1);
        AgentUser childAgent2 = createTestAgent(4, "子代理2", 1, 1);
        AgentUser grandChildAgent = createTestAgent(5, "孙代理", 3, 2);
        
        List<AgentUser> allAgents = Arrays.asList(rootAgent1, rootAgent2, childAgent1, childAgent2, grandChildAgent);
        
        // 模拟 Mapper 返回
        when(agentUserMapper.findAll()).thenReturn(allAgents);

        // 执行测试
        ServerResponse<List<AgentUser>> result = agentUserBizService.getAgentTreeStructure();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("查询成功", result.getMsg());
        
        List<AgentUser> treeStructure = result.getData();
        assertNotNull(treeStructure);
        assertEquals(2, treeStructure.size()); // 应该有2个根节点
        
        // 验证第一个根节点的子节点
        AgentUser firstRoot = treeStructure.get(0);
        assertNotNull(firstRoot.getChildren());
        assertEquals(2, firstRoot.getChildren().size()); // 第一个根节点应该有2个子节点
        
        // 验证孙节点
        AgentUser firstChild = firstRoot.getChildren().get(0);
        if (firstChild.getId().equals(3)) { // 如果是子代理1
            assertNotNull(firstChild.getChildren());
            assertEquals(1, firstChild.getChildren().size()); // 应该有1个孙节点
        }

        // 验证 Mapper 方法被调用
        verify(agentUserMapper, times(1)).findAll();
    }

    @Test
    public void testGetAgentTreeStructure_EmptyList() {
        // 模拟 Mapper 返回空列表
        when(agentUserMapper.findAll()).thenReturn(Arrays.asList());

        // 执行测试
        ServerResponse<List<AgentUser>> result = agentUserBizService.getAgentTreeStructure();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("查询成功", result.getMsg());
        
        List<AgentUser> treeStructure = result.getData();
        assertNotNull(treeStructure);
        assertEquals(0, treeStructure.size());

        // 验证 Mapper 方法被调用
        verify(agentUserMapper, times(1)).findAll();
    }

    @Test
    public void testGetAgentTreeStructure_DatabaseException() {
        // 模拟数据库异常
        when(agentUserMapper.findAll()).thenThrow(new RuntimeException("数据库连接失败"));

        // 执行测试
        ServerResponse<List<AgentUser>> result = agentUserBizService.getAgentTreeStructure();

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getMsg().contains("查询失败"));

        // 验证 Mapper 方法被调用
        verify(agentUserMapper, times(1)).findAll();
    }

    /**
     * 创建测试用的代理用户
     */
    private AgentUser createTestAgent(Integer id, String name, Integer parentId, Integer level) {
        AgentUser agent = new AgentUser();
        agent.setId(id);
        agent.setAgentName(name);
        agent.setAgentRealName(name);
        agent.setAgentPhone("1380013800" + id);
        agent.setParentId(parentId);
        agent.setAgentLevel(level);
        agent.setAddTime(new Date());
        agent.setIsLock(0);
        agent.setPoundageScale(new BigDecimal("0.01"));
        agent.setDeferredFeesScale(new BigDecimal("0.01"));
        agent.setReceiveDividendsScale(new BigDecimal("0.01"));
        agent.setTotalMoney(new BigDecimal("0"));
        return agent;
    }
}
