package com.nq.service;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dao.UserMapper;
import com.nq.pojo.AgentUser;
import com.nq.pojo.User;
import com.nq.service.impl.UserBizServiceImpl;
import com.nq.utils.UserInfoUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * UserBizService 测试类
 */
public class UserBizServiceTest {

    @Mock
    private UserMapper userMapper;

    @Mock
    private AgentUserBizService agentUserBizService;

    @Mock
    private IUserSignatureService iUserSignatureService;

    @Mock
    private IUserPositionService iUserPositionService;

    @Mock
    private IUserRechargeService iUserRechargeService;

    @Mock
    private IUserWithdrawService iUserWithdrawService;

    @Mock
    private IUserStockSubscribeBizService userStockSubscribeBizService;

    @Mock
    private HttpServletRequest request;

    @InjectMocks
    private UserBizServiceImpl userBizService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testListByAdminWithSubAgents_Success() {
        // 准备测试数据
        Integer agentId = 1;
        String realName = "测试用户";
        String phone = "***********";
        Integer accountType = 0;
        Integer status = 2;
        int pageNum = 1;
        int pageSize = 10;

        // 创建测试代理用户
        AgentUser currentAgent = createTestAgent(1, "当前代理", 0, 0);
        AgentUser subAgent1 = createTestAgent(2, "下级代理1", 1, 1);
        AgentUser subAgent2 = createTestAgent(3, "下级代理2", 1, 1);
        List<AgentUser> subordinateAgents = Arrays.asList(subAgent1, subAgent2);

        // 创建测试用户
        User user1 = createTestUser(1, "用户1", "***********", 1);
        User user2 = createTestUser(2, "用户2", "***********", 2);
        List<User> users = Arrays.asList(user1, user2);

        // 模拟静态方法调用
        try (MockedStatic<UserInfoUtil> mockedUserInfoUtil = mockStatic(UserInfoUtil.class)) {
            mockedUserInfoUtil.when(() -> UserInfoUtil.getCurrentAgentUser(request))
                    .thenReturn(currentAgent);

            // 模拟服务调用
            ServerResponse<List<AgentUser>> subordinateResponse = ServerResponse.createBySuccess("查询成功", subordinateAgents);
            when(agentUserBizService.getSubordinateAgents(agentId)).thenReturn(subordinateResponse);
            when(userMapper.listByAdminWithAgentIds(eq(realName), eq(phone), any(List.class), eq(accountType), eq(status)))
                    .thenReturn(users);

            // 执行测试
            ServerResponse<PageInfo> result = userBizService.listByAdminWithSubAgents(
                    realName, phone, agentId, accountType, pageNum, pageSize, status, request);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccess());
            
            PageInfo pageInfo = result.getData();
            assertNotNull(pageInfo);
            assertEquals(2, pageInfo.getList().size());

            // 验证方法调用
            verify(agentUserBizService, times(1)).getSubordinateAgents(agentId);
            verify(userMapper, times(1)).listByAdminWithAgentIds(eq(realName), eq(phone), any(List.class), eq(accountType), eq(status));
        }
    }

    @Test
    public void testListByAdminWithSubAgents_NoAgentId() {
        // 准备测试数据
        String realName = "测试用户";
        String phone = "***********";
        Integer accountType = 0;
        Integer status = 2;
        int pageNum = 1;
        int pageSize = 10;

        // 创建测试代理用户
        AgentUser currentAgent = createTestAgent(1, "当前代理", 0, 0);

        // 模拟静态方法调用
        try (MockedStatic<UserInfoUtil> mockedUserInfoUtil = mockStatic(UserInfoUtil.class)) {
            mockedUserInfoUtil.when(() -> UserInfoUtil.getCurrentAgentUser(request))
                    .thenReturn(currentAgent);

            // 模拟服务调用
            ServerResponse<List<AgentUser>> subordinateResponse = ServerResponse.createBySuccess("查询成功", Arrays.asList());
            when(agentUserBizService.getSubordinateAgents(currentAgent.getId())).thenReturn(subordinateResponse);
            when(userMapper.listByAdminWithAgentIds(eq(realName), eq(phone), any(List.class), eq(accountType), eq(status)))
                    .thenReturn(Arrays.asList());

            // 执行测试（不传递agentId）
            ServerResponse<PageInfo> result = userBizService.listByAdminWithSubAgents(
                    realName, phone, null, accountType, pageNum, pageSize, status, request);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccess());

            // 验证使用了当前代理的ID
            verify(agentUserBizService, times(1)).getSubordinateAgents(currentAgent.getId());
        }
    }

    @Test
    public void testListByAdminWithSubAgents_NoCurrentAgent() {
        // 准备测试数据
        String realName = "测试用户";
        String phone = "***********";
        Integer accountType = 0;
        Integer status = 2;
        int pageNum = 1;
        int pageSize = 10;

        // 模拟静态方法调用返回null
        try (MockedStatic<UserInfoUtil> mockedUserInfoUtil = mockStatic(UserInfoUtil.class)) {
            mockedUserInfoUtil.when(() -> UserInfoUtil.getCurrentAgentUser(request))
                    .thenReturn(null);

            // 执行测试
            ServerResponse<PageInfo> result = userBizService.listByAdminWithSubAgents(
                    realName, phone, null, accountType, pageNum, pageSize, status, request);

            // 验证结果
            assertNotNull(result);
            assertFalse(result.isSuccess());
            assertEquals("无法获取当前代理用户信息", result.getMsg());

            // 验证没有调用其他服务
            verify(agentUserBizService, never()).getSubordinateAgents(any());
            verify(userMapper, never()).listByAdminWithAgentIds(any(), any(), any(), any(), any());
        }
    }

    @Test
    public void testListByAdminWithSubAgents_Exception() {
        // 准备测试数据
        Integer agentId = 1;

        // 模拟异常
        when(agentUserBizService.getSubordinateAgents(agentId))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行测试
        ServerResponse<PageInfo> result = userBizService.listByAdminWithSubAgents(
                null, null, agentId, null, 1, 10, null, request);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getMsg().contains("查询失败"));
    }

    /**
     * 创建测试用的代理用户
     */
    private AgentUser createTestAgent(Integer id, String name, Integer parentId, Integer level) {
        AgentUser agent = new AgentUser();
        agent.setId(id);
        agent.setAgentName(name);
        agent.setAgentRealName(name);
        agent.setAgentPhone("1380013800" + id);
        agent.setParentId(parentId);
        agent.setAgentLevel(level);
        agent.setAddTime(new Date());
        agent.setIsLock(0);
        agent.setPoundageScale(new BigDecimal("0.01"));
        agent.setDeferredFeesScale(new BigDecimal("0.01"));
        agent.setReceiveDividendsScale(new BigDecimal("0.01"));
        agent.setTotalMoney(new BigDecimal("0"));
        return agent;
    }

    /**
     * 创建测试用的用户
     */
    private User createTestUser(Integer id, String realName, String phone, Integer agentId) {
        User user = new User();
        user.setId(id);
        user.setRealName(realName);
        user.setPhone(phone);
        user.setAgentId(agentId);
        user.setAccountType(0);
        user.setIsActive(2);
        user.setIsLock(0);
        user.setUserAmt(new BigDecimal("10000"));
        user.setEnableAmt(new BigDecimal("8000"));
        return user;
    }
}
