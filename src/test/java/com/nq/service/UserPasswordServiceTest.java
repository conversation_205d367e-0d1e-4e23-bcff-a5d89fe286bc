package com.nq.service;

import com.nq.common.ServerResponse;
import com.nq.dao.UserMapper;
import com.nq.pojo.User;
import com.nq.service.impl.UserServiceImpl;
import com.nq.vo.UserPasswordVO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 用户密码查询功能测试类
 */
public class UserPasswordServiceTest {

    @Mock
    private UserMapper userMapper;

    @InjectMocks
    private UserServiceImpl userService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetUserPasswords_Success() {
        // 准备测试数据
        Integer userId = 1;
        User mockUser = new User();
        mockUser.setId(userId);
        mockUser.setPhone("13800138000");
        mockUser.setRealName("测试用户");
        mockUser.setUserPwd("loginPassword123");
        mockUser.setWithPwd("withdrawPassword123");
        mockUser.setWithdrawalPwd("withdrawalPassword123");

        // 模拟 Mapper 返回
        when(userMapper.selectUserPasswordsById(userId)).thenReturn(mockUser);

        // 执行测试
        ServerResponse result = userService.getUserPasswords(userId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("查询成功", result.getMsg());
        
        UserPasswordVO passwordInfo = (UserPasswordVO) result.getData();
        assertNotNull(passwordInfo);
        assertEquals(userId, passwordInfo.getUserId());
        assertEquals("13800138000", passwordInfo.getPhone());
        assertEquals("测试用户", passwordInfo.getRealName());
        assertEquals("loginPassword123", passwordInfo.getLoginPassword());
        assertEquals("withdrawPassword123", passwordInfo.getWithdrawPassword());
        assertEquals("withdrawalPassword123", passwordInfo.getWithdrawalPassword());

        // 验证 Mapper 方法被调用
        verify(userMapper, times(1)).selectUserPasswordsById(userId);
    }

    @Test
    public void testGetUserPasswords_UserNotFound() {
        // 准备测试数据
        Integer userId = 999;

        // 模拟 Mapper 返回 null
        when(userMapper.selectUserPasswordsById(userId)).thenReturn(null);

        // 执行测试
        ServerResponse result = userService.getUserPasswords(userId);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("用户不存在", result.getMsg());
        assertNull(result.getData());

        // 验证 Mapper 方法被调用
        verify(userMapper, times(1)).selectUserPasswordsById(userId);
    }

    @Test
    public void testGetUserPasswords_NullUserId() {
        // 执行测试
        ServerResponse result = userService.getUserPasswords(null);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("用户ID不能为空", result.getMsg());
        assertNull(result.getData());

        // 验证 Mapper 方法未被调用
        verify(userMapper, never()).selectUserPasswordsById(any());
    }

    @Test
    public void testGetUserPasswords_DatabaseException() {
        // 准备测试数据
        Integer userId = 1;

        // 模拟数据库异常
        when(userMapper.selectUserPasswordsById(userId))
            .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行测试
        ServerResponse result = userService.getUserPasswords(userId);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getMsg().contains("查询失败"));
        assertNull(result.getData());

        // 验证 Mapper 方法被调用
        verify(userMapper, times(1)).selectUserPasswordsById(userId);
    }

    @Test
    public void testUserPasswordVO_EffectiveWithdrawPassword() {
        // 测试 UserPasswordVO 的 getEffectiveWithdrawPassword 方法
        UserPasswordVO vo = new UserPasswordVO();
        
        // 测试两个密码都为空的情况
        assertNull(vo.getEffectiveWithdrawPassword());
        
        // 测试只有旧密码的情况
        vo.setWithdrawPassword("oldPassword");
        assertEquals("oldPassword", vo.getEffectiveWithdrawPassword());
        
        // 测试两个密码都有的情况，应该返回新密码
        vo.setWithdrawalPassword("newPassword");
        assertEquals("newPassword", vo.getEffectiveWithdrawPassword());
        
        // 测试新密码为空字符串的情况，应该返回旧密码
        vo.setWithdrawalPassword("");
        assertEquals("oldPassword", vo.getEffectiveWithdrawPassword());
        
        // 测试新密码为空格的情况，应该返回旧密码
        vo.setWithdrawalPassword("   ");
        assertEquals("oldPassword", vo.getEffectiveWithdrawPassword());
    }
}
