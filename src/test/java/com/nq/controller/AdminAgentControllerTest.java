package com.nq.controller;

import com.nq.controller.backend.AdminAgentController;
import com.nq.service.IAgentUserService;
import com.nq.common.ServerResponse;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * AdminAgentController 测试类
 * 主要测试批量更新代理服务线功能
 */
public class AdminAgentControllerTest {

    @Mock
    private IAgentUserService iAgentUserService;

    @InjectMocks
    private AdminAgentController adminAgentController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testBatchUpdateServiceLine_Success() {
        // 准备测试数据
        List<Integer> agentIds = Arrays.asList(1, 2, 3);
        String serviceLine = "VIP服务线";
        
        // 模拟服务层返回成功响应
        ServerResponse mockResponse = ServerResponse.createBySuccessMsg("批量更新服务线成功，共更新3个代理");
        when(iAgentUserService.batchUpdateServiceLine(agentIds, serviceLine)).thenReturn(mockResponse);

        // 执行测试
        ServerResponse result = adminAgentController.batchUpdateServiceLine(agentIds, serviceLine);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("批量更新服务线成功，共更新3个代理", result.getMsg());
        
        // 验证服务层方法被调用
        verify(iAgentUserService, times(1)).batchUpdateServiceLine(agentIds, serviceLine);
    }

    @Test
    public void testBatchUpdateServiceLine_EmptyAgentIds() {
        // 准备测试数据
        List<Integer> agentIds = Arrays.asList();
        String serviceLine = "VIP服务线";
        
        // 模拟服务层返回错误响应
        ServerResponse mockResponse = ServerResponse.createByErrorMsg("代理ID列表不能为空");
        when(iAgentUserService.batchUpdateServiceLine(agentIds, serviceLine)).thenReturn(mockResponse);

        // 执行测试
        ServerResponse result = adminAgentController.batchUpdateServiceLine(agentIds, serviceLine);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("代理ID列表不能为空", result.getMsg());
    }

    @Test
    public void testBatchUpdateServiceLine_EmptyServiceLine() {
        // 准备测试数据
        List<Integer> agentIds = Arrays.asList(1, 2, 3);
        String serviceLine = "";
        
        // 模拟服务层返回错误响应
        ServerResponse mockResponse = ServerResponse.createByErrorMsg("服务线不能为空");
        when(iAgentUserService.batchUpdateServiceLine(agentIds, serviceLine)).thenReturn(mockResponse);

        // 执行测试
        ServerResponse result = adminAgentController.batchUpdateServiceLine(agentIds, serviceLine);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("服务线不能为空", result.getMsg());
    }

    @Test
    public void testBatchUpdateServiceLine_NoAgentsFound() {
        // 准备测试数据
        List<Integer> agentIds = Arrays.asList(999, 1000, 1001); // 不存在的代理ID
        String serviceLine = "VIP服务线";
        
        // 模拟服务层返回错误响应
        ServerResponse mockResponse = ServerResponse.createByErrorMsg("没有找到需要更新的代理");
        when(iAgentUserService.batchUpdateServiceLine(agentIds, serviceLine)).thenReturn(mockResponse);

        // 执行测试
        ServerResponse result = adminAgentController.batchUpdateServiceLine(agentIds, serviceLine);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("没有找到需要更新的代理", result.getMsg());
    }
}
