package com.nq.controller.backend;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.nq.common.ServerResponse;
import com.nq.pojo.SysConfig;
import com.nq.service.SysConfigService;
import com.nq.utils.QiniuUtil;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2025/4/4 13:09
 */
@RestController
@RequestMapping("/admin/system/")
@AllArgsConstructor
@Slf4j
public class AdminSystemConfigController {

    private final SysConfigService sysConfigService;

    @RequestMapping("getInitConfig")
    public ServerResponse getInitConfig() {
        SysConfig sysConfig = sysConfigService.getSysConfig();
        return ServerResponse.createBySuccess(sysConfig);
    }

    @RequestMapping("getUploadToken")
    public ServerResponse getUpToken(@RequestParam("key") String key) {
        String token = QiniuUtil.getToken(key);
        log.info("getUploadToken,key={},token={}", key, token);
        return ServerResponse.createBySuccess(token);
    }
    
    /**
     * 更新系统配置
     * @param sysConfig 系统配置参数
     * @return 更新结果
     */
    @RequestMapping("updateConfig")
    public ServerResponse updateConfig(SysConfig sysConfig) {
        log.info("更新系统配置, sysConfig={}", sysConfig);
        int result = sysConfigService.updateSysConfig(sysConfig);
        if (result > 0) {
            return ServerResponse.createBySuccessMsg("配置更新成功");
        }
        return ServerResponse.createByErrorMsg("配置更新失败");
    }

}
