package com.nq.controller.backend;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.eum.cash.WithdrawOrderStatusEum;
import com.nq.pojo.UserWithdraw;
import com.nq.service.IUserWithdrawService;
import com.nq.service.PendingWithdrawOrderService;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;

@Controller
@RequestMapping({"/admin/withdraw/"})
public class AdminWithDrawController {
    private static final Logger log = LoggerFactory.getLogger(AdminWithDrawController.class);

    @Autowired
    IUserWithdrawService iUserWithdrawService;

    @Resource
    PendingWithdrawOrderService pendingWithdrawOrderService;

    // 分页查询资金管理 提现列表信息及模糊查询
    @RequestMapping({"list.do"})
    @ResponseBody
    public ServerResponse<PageInfo> list(@RequestParam(value = "agentId", required = false) Integer agentId,
        @RequestParam(value = "userId", required = false) Integer userId,
        @RequestParam(value = "realName", required = false) String realName,
        @RequestParam(value = "state", required = false) Integer state,
        @RequestParam(value = "beginTime", required = false) String beginTime,
        @RequestParam(value = "endTime", required = false) String endTime,
        @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
        @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
        @RequestParam(value = "accountType", required = false) Integer accountType,
        @RequestParam(value = "phone", required = false) String phone, HttpServletRequest request) {
        return this.iUserWithdrawService.listByAdmin(agentId, userId, realName, state, beginTime, endTime, request,
            pageNum, pageSize, accountType, phone);
    }

    // 导出提现记录
    @RequestMapping({"export.do"})
    @ResponseBody
    public void export(@RequestParam(value = "agentId", required = false) Integer agentId,
        @RequestParam(value = "userId", required = false) Integer userId,
        @RequestParam(value = "realName", required = false) String realName,
        @RequestParam(value = "state", required = false) Integer state,
        @RequestParam(value = "beginTime", required = false) String beginTime,
        @RequestParam(value = "endTime", required = false) String endTime,
        @RequestParam(value = "accountType", required = false) Integer accountType, HttpServletRequest request,
        HttpServletResponse response) {
        List<UserWithdraw> userRechargeList = this.iUserWithdrawService.exportByAdmin(agentId, userId, realName, state,
            beginTime, endTime, request, accountType);
        Workbook workbook =
            ExcelExportUtil.exportExcel(new ExportParams("提现导出", "提现数据"), UserWithdraw.class, userRechargeList);
        try {
            ServletOutputStream outputStream = response.getOutputStream();

            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("导出提现数据失败", e);
        }
    }

    // 修改资金管理 提现列表 提现状态
    @RequestMapping({"updateState.do"})
    @ResponseBody
    public ServerResponse updateState(@RequestParam(value = "withId", required = false) Integer withId,
        @RequestParam(value = "state", required = false) Integer state,
        @RequestParam(value = "authMsg", required = false) String authMsg, HttpServletRequest request) {
        ServerResponse serverResponse = null;
        try {
            serverResponse = this.iUserWithdrawService.updateState(withId, state, authMsg, request);
        } catch (Exception e) {
            log.error("admin修改充值订单状态出错 ，异常 = {}", e);
        }
        return serverResponse;
    }

    // 删除资金记录
    @RequestMapping({"deleteWithdraw.do"})
    @ResponseBody
    public ServerResponse deleteWithdraw(Integer withdrawId) {
        return this.iUserWithdrawService.deleteWithdraw(withdrawId);
    }

    @RequestMapping({"getPendingOrderNum.do"})
    @ResponseBody
    public ServerResponse getPendingWithdrawOrderNum() {
        return ServerResponse.createBySuccess(pendingWithdrawOrderService.getNums());
    }

    /**
     * 首页展示待处理的订单数量
     * 
     * @return
     */
    @RequestMapping({"getRealPendingOrderNum.do"})
    @ResponseBody
    public ServerResponse getRealPendingOrderNum() {
        return ServerResponse.createBySuccess(
            iUserWithdrawService.selectCountWithdrawOrderNumByStatus(WithdrawOrderStatusEum.PENDING.getValue()));
    }

    /**
     *
     * @param accountType
     * @return
     */
    @RequestMapping({"countRechargeAmount.do"})
    @ResponseBody
    public ServerResponse countRechargeAmount(
        @RequestParam(value = "accountType", defaultValue = "0") Integer accountType,
        @RequestParam(value = "agentId", required = false) Integer agentId) {
        Map<String, Object> data = new HashMap<String, Object>();

        // 累计提现金额- 真实用户数据
        BigDecimal totalWithdrawAmount = this.iUserWithdrawService.CountSpWithSumAmtByState(accountType, agentId);
        // 今日提现金额- 真实用户数据
        BigDecimal todayWithdrawAmount = this.iUserWithdrawService.CountSpWithSumAmTodaytByState(accountType, agentId);
        data.put("todayWithdrawAmount", Optional.ofNullable(todayWithdrawAmount).orElse(BigDecimal.ZERO));
        data.put("totalWithdrawAmount", Optional.ofNullable(totalWithdrawAmount).orElse(BigDecimal.ZERO));

        return ServerResponse.createBySuccess(data);
    }
}
