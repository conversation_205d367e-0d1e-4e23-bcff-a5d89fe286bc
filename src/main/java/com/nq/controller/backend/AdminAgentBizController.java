package com.nq.controller.backend;

import com.nq.common.ServerResponse;
import com.nq.pojo.AgentUser;
import com.nq.service.AgentUserBizService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 代理用户业务管理控制器
 * 提供代理用户的高级业务功能
 */
@Controller
@RequestMapping("/admin/agentBiz/")
public class AdminAgentBizController {

    @Autowired
    private AgentUserBizService agentUserBizService;

    /**
     * 根据当前代理ID获取所有下级的代理用户
     * @param agentId 代理ID
     * @return 下级代理用户列表
     */
    @RequestMapping("getSubordinateAgents.do")
    @ResponseBody
    public ServerResponse<List<AgentUser>> getSubordinateAgents(@RequestParam("agentId") Integer agentId) {
        return agentUserBizService.getSubordinateAgents(agentId);
    }

    /**
     * 获取所有代理用户的树形结构
     * @return 树形结构的代理用户列表
     */
    @RequestMapping("getAgentTreeStructure.do")
    @ResponseBody
    public ServerResponse<List<AgentUser>> getAgentTreeStructure() {
        return agentUserBizService.getAgentTreeStructure();
    }
}
