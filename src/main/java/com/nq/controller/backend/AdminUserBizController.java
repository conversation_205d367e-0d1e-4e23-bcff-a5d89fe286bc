package com.nq.controller.backend;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.service.UserBizService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户业务管理控制器
 * 提供用户的高级业务功能
 */
@Controller
@RequestMapping("/admin/userBiz/")
public class AdminUserBizController {

    @Autowired
    private UserBizService userBizService;

    /**
     * 管理员查询用户列表（包含当前代理下所有代理的用户）
     * @param realName 真实姓名
     * @param phone 手机号
     * @param agentId 代理ID
     * @param accountType 账户类型
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param status 状态
     * @param request 请求对象
     * @return 用户列表分页数据
     */
    @RequestMapping("listByAdminWithSubAgents.do")
    @ResponseBody
    public ServerResponse<PageInfo> listByAdminWithSubAgents(
            @RequestParam(value = "realName", required = false) String realName,
            @RequestParam(value = "phone", required = false) String phone,
            @RequestParam(value = "agentId", required = false) Integer agentId,
            @RequestParam(value = "accountType", required = false) Integer accountType,
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(value = "status", required = false) Integer status,
            HttpServletRequest request) {
        
        return userBizService.listByAdminWithSubAgents(realName, phone, agentId, accountType, 
                                                      pageNum, pageSize, status, request);
    }
}
