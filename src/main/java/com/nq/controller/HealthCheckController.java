package com.nq.controller;

import com.nq.common.ServerResponse;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 域名检查
 * @author: Petter
 * @create: 2024-11-20
 **/
@RestController
public class HealthCheckController {

    @RequestMapping({"/health-check"})
    @ResponseBody
    public ServerResponse healthCheck() {
        return ServerResponse.createBySuccess("yinhezhengquan");
    }

}
