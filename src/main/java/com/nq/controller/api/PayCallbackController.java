package com.nq.controller.api;

import com.alibaba.fastjson.JSON;
import com.nq.service.pay.PayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付回调控制器
 * 处理第三方支付平台的回调请求
 */
@Controller
@RequestMapping("/api/pay")
@Slf4j
public class PayCallbackController {

    @Autowired
    private PayService payService;

    /**
     * 支付回调接口
     *
     * @param request  HTTP请求对象
     * @param response HTTP响应对象
     * @throws Exception 异常信息
     */
    @RequestMapping("/notify/yyggg")
    @ResponseBody
    public void yyGggPayNotify(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("【yyGggPayNotify】收到支付回调请求");

        try {
            // 获取所有请求参数
            Map<String, Object> paramMap = new HashMap<>();
            Enumeration<String> paramNames = request.getParameterNames();
            while (paramNames.hasMoreElements()) {
                String paramName = paramNames.nextElement();
                String paramValue = request.getParameter(paramName);
                paramMap.put(paramName, paramValue);
            }

            // 记录回调参数
            log.info("【yyGggPayNotify】支付回调参数：{}", JSON.toJSONString(paramMap));

            // 处理回调
            payService.payCallback(paramMap, request, response);
        } catch (Exception e) {
            log.error("【yyGggPayNotify】处理支付回调异常", e);
            response.getWriter().write("fail");
        }
    }

    /**
     * 支付同步回调接口
     *
     * @param request  HTTP请求对象
     * @param response HTTP响应对象
     * @throws Exception 异常信息
     */
    @RequestMapping("/return/yyggg")
    public String yyGggPayReturn(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("【yyGggPayReturn】收到支付同步回调请求");

        try {
            // 获取所有请求参数
            Map<String, Object> paramMap = new HashMap<>();
            Enumeration<String> paramNames = request.getParameterNames();
            while (paramNames.hasMoreElements()) {
                String paramName = paramNames.nextElement();
                String paramValue = request.getParameter(paramName);
                paramMap.put(paramName, paramValue);
            }

            // 记录回调参数
            log.info("【yyGggPayReturn】支付同步回调参数：{}", JSON.toJSONString(paramMap));

            // 获取订单号
            String mchOrderNo = (String) paramMap.get("mchOrderNo");
            log.info("【yyGggPayReturn】支付同步回调，订单号：{}", mchOrderNo);

            // 跳转到充值列表页面
            return "redirect:/homes/#/rechargelist";
        } catch (Exception e) {
            log.error("【yyGggPayReturn】处理支付同步回调异常", e);
            // 发生异常也跳转到充值列表页面
            return "redirect:/homes/#/rechargelist";
        }
    }
}
