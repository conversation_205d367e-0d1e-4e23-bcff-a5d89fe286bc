package com.nq.controller.app.user;

import com.nq.common.ServerResponse;
import com.nq.common.crypto.Decrypt;
import com.nq.common.crypto.Encrypt;
import com.nq.controller.app.user.vo.newstock.FindPageNewStockReq;
import com.nq.controller.app.user.vo.newstock.GetWinLotteryReq;
import com.nq.controller.app.vo.IntIdReq;
import com.nq.pojo.UserStockSubscribe;
import com.nq.service.IStockSubscribeService;
import com.nq.service.IUserStockSubscribeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2025/3/10 14:36
 */

@RestController
@RequestMapping("/user/")
public class UserNewStockController {

    @Autowired
    IUserStockSubscribeService iUserStockSubscribeService;
    @Autowired
    IStockSubscribeService iStockSubscribeService;

    @RequestMapping({"newStockList.do"})
    @Encrypt
    @Decrypt
    public ServerResponse list(@RequestBody FindPageNewStockReq req, HttpServletRequest request) {
        return this.iStockSubscribeService.list(req.getPageNum(), req.getPageSize(), req.getName(), req.getCode(),
                req.getZt(), req.getIsLock(), req.getType(), request);
    }

    /**
     * 新股申购 添加
     *
     * @param
     * @return
     */
    @RequestMapping({"add.do"})
    @Encrypt
    @Decrypt
    public ServerResponse add(@RequestBody UserStockSubscribe model, HttpServletRequest request) throws Exception {
        return this.iUserStockSubscribeService.insert(model, request);
    }

    // 新股申购-中签记录
    @RequestMapping({"getzqjl.do"})
    @Encrypt
    @Decrypt
    public ServerResponse getOneSubscribeByUserIdAndType(@RequestBody GetWinLotteryReq req,
                                                         HttpServletRequest request) {
        return this.iUserStockSubscribeService.getzqjkl(req.getStatus(), request);
    }

    /*新股申购-认缴 用户提交金额*/
    @RequestMapping({"submitSubscribe.do"})
    @Encrypt
    @Decrypt
    public ServerResponse userSubmit(@RequestBody IntIdReq req, HttpServletRequest request) {
        return this.iUserStockSubscribeService.userSubmit(req.getId(), request);
    }

}