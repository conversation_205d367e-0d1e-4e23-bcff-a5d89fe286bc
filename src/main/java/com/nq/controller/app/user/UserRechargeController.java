package com.nq.controller.app.user;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.common.crypto.Decrypt;
import com.nq.common.crypto.Encrypt;
import com.nq.controller.app.user.vo.recharge.FindPageRechargeReq;
import com.nq.controller.app.user.vo.recharge.InMoneyReq;
import com.nq.service.IUserRechargeService;
import com.nq.service.UserRechargeBizService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping({"/user/recharge/"})
@Slf4j
public class UserRechargeController {

    @Autowired
    IUserRechargeService iUserRechargeService;
    @Autowired
    UserRechargeBizService userRechargeBizService;


    // 分页查询所有充值记录
    @RequestMapping({"list.do"})
    @Encrypt
    @Decrypt
    public ServerResponse<PageInfo> list(@RequestBody FindPageRechargeReq req, HttpServletRequest request) {
        return this.iUserRechargeService.findUserChargeList(req.getPayChannel(), req.getOrderStatus(), request,
                req.getPageNum(), req.getPageSize());
    }

    // 账户线下充值转账 创建充值订单
    @RequestMapping({"inMoney.do"})
    @Encrypt
    @Decrypt
    public ServerResponse inMoney(@RequestBody InMoneyReq req, HttpServletRequest request) {
        ServerResponse serverResponse = userRechargeBizService.enableRecharge(request);
        if (serverResponse.isSuccess()) {
            return this.iUserRechargeService.inMoney(req.getAmt(), req.getPayType(), req.getPassword(), request);
        }
        return serverResponse;
    }
}
