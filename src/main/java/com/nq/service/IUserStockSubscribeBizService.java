package com.nq.service;

import java.math.BigDecimal;

import com.nq.common.ServerResponse;

/**
 * <AUTHOR>
 * @since 2024/12/24 13:58
 */
public interface IUserStockSubscribeBizService {

    BigDecimal updateSubscribeByRecharge(Integer userId, BigDecimal rechargeAmount);

    void updateSubscribeBySellStock(Integer userId, BigDecimal profitAmount);

    ServerResponse buyBackNewStock(Integer id, String buyBackPrice);

    BigDecimal sumBuyBackProfit(Integer userId);
}
