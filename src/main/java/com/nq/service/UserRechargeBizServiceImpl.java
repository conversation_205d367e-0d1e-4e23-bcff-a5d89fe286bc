package com.nq.service;

import com.nq.common.ServerResponse;
import com.nq.dao.UserRechargeMapper;
import com.nq.pojo.SysConfig;
import com.nq.pojo.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2025/5/28 18:54
 */
@Service
public class UserRechargeBizServiceImpl implements UserRechargeBizService {
    @Autowired
    SysConfigService sysConfigService;
    @Autowired
    UserRechargeMapper userRechargeMapper;
    @Autowired
    IUserService iUserService;

    @Override
    public ServerResponse enableRecharge(HttpServletRequest request) {
        // 检查支付开关
        SysConfig sysConfig = sysConfigService.getSysConfig();
        if (sysConfig != null && sysConfig.getPayEnabled() != null && sysConfig.getPayEnabled() == 0) {
            // 支付已关闭，返回配置的提示信息
            String message = sysConfig.getPayDisabledMessage();
            if (StringUtils.isBlank(message)) {
                message = "今日银证额度已满，请明日开市后再进行注资事宜。";
            }
            return ServerResponse.createByErrorMsg(message);
        }
        // 检查每日充值次数限制
        if (sysConfig != null && sysConfig.getRechargeLimitEnabled() != null && sysConfig.getRechargeLimitEnabled() == 1) {
            User user = iUserService.getCurrentRefreshUser(request);
            if (user == null) {
                return ServerResponse.createByErrorMsg("请先登录");
            }
            Integer userId = user.getId();
            // 获取用户当日充值次数
            int dailyRechargeCount = userRechargeMapper.countUserDailyRecharges(user.getId());
            if (dailyRechargeCount >= sysConfig.getRechargeLimitDaily()) {
                String message = sysConfig.getRechargeLimitMessage();
                if (StringUtils.isBlank(message)) {
                    message = "您今日充值次数已达上限，请明日再试或联系客服";
                }
                return ServerResponse.createByErrorMsg(message);
            }
        }
        return ServerResponse.createBySuccess();
    }
}
