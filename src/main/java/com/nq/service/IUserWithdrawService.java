package com.nq.service;

import java.math.BigDecimal;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.UserWithdraw;

public interface IUserWithdrawService {
    ServerResponse outMoney(String paramString, String with_Pwd, HttpServletRequest paramHttpServletRequest)
        throws Exception;

    ServerResponse<PageInfo> findUserWithList(String paramString, HttpServletRequest paramHttpServletRequest,
        int paramInt1, int paramInt2);

    ServerResponse userCancel(Integer paramInteger);

    ServerResponse listByAgent(Integer paramInteger1, String paramString, Integer paramInteger2,
        HttpServletRequest paramHttpServletRequest, int paramInt1, int paramInt2);

    ServerResponse<PageInfo> listByAdmin(Integer paramInteger1, Integer paramInteger2, String paramString1,
        Integer paramInteger3, String paramString2, String paramString3, HttpServletRequest paramHttpServletRequest,
        int paramInt1, int paramInt2, Integer accountType, String phone);

    ServerResponse updateState(Integer paramInteger1, Integer paramInteger2, String paramString,
        HttpServletRequest request) throws Exception;

    int deleteByUserId(Integer paramInteger);

    BigDecimal CountSpWithSumAmtByState(Integer accountType, Integer agentId);

    BigDecimal CountSpWithSumAmTodaytByState(Integer accountType, Integer agentId);

    ServerResponse deleteWithdraw(Integer withdrawId);

    List<UserWithdraw> exportByAdmin(Integer agentId, Integer userId, String realName, Integer state, String beginTime,
        String endTime, HttpServletRequest request, Integer accountType);

    /**
     * 根据用户ID和状态查询累计数据
     * 
     * @param userId
     * @param status
     * @return
     */
    BigDecimal getUserTotalWithdrawAmountByStatus(Integer userId, Integer status);

    Long selectCountWithdrawOrderNumByStatus(Integer withStatus);
}
