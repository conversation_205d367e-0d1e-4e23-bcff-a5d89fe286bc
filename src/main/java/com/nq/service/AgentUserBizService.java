package com.nq.service;

import com.nq.common.ServerResponse;
import com.nq.pojo.AgentUser;

import java.util.List;

/**
 * 代理用户业务服务接口
 * <AUTHOR>
 * @since 2025/6/3 17:29
 */
public interface AgentUserBizService {

    /**
     * 根据当前代理ID获取所有下级的代理用户
     * @param agentId 当前代理ID
     * @return 下级代理用户列表
     */
    ServerResponse<List<AgentUser>> getSubordinateAgents(Integer agentId);

    /**
     * 获取所有代理用户，然后组装成树形结构
     * @return 树形结构的代理用户列表
     */
    ServerResponse<List<AgentUser>> getAgentTreeStructure();
}
