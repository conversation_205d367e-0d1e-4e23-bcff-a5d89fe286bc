package com.nq.service.pay;

import com.nq.pojo.UserRecharge;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/5/28 16:41
 */
public interface PayService {
    String sendThirdPayRequest(UserRecharge userRecharge, HttpServletRequest request);

    void payCallback(Map<String, Object> callbackRequestParamMap, HttpServletRequest request,
                     HttpServletResponse response) throws Exception;
}
