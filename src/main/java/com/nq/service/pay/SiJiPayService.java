package com.nq.service.pay;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.nq.dao.UserMapper;
import com.nq.dao.UserRechargeMapper;
import com.nq.pojo.User;
import com.nq.pojo.UserRecharge;
import com.nq.utils.pay.PayDigestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/28 16:15
 */
//@Service
@Slf4j
public class SiJiPayService implements PayService {

    @Resource
    private UserRechargeMapper userRechargeMapper;
    @Resource
    private UserMapper userMapper;

    private static final String key = "MMCG5KFNBRDJ2ZQYAH5L9XASPXMFWZCM37P3TRQUQP6Y0LWJ9DNWOPG4Y08OGVAOE3DZFSIVYQVNPEDKKRRV0VT9KFKX5FFJWTQX7MPKQ1INUJGQM7U9WJTA5DEN8C2C";
    private static final String appId = "1f56efd325d44824b603173ee64f5b28";
    private static final Integer productId = 1087;

    @Override
    public String sendThirdPayRequest(UserRecharge userRecharge, HttpServletRequest request) {
        String domain = request.getHeader("host");
        if (StrUtil.isEmpty(domain)) {
            domain = request.getServerName();
        }
        String fullDomain = "https://" + domain + "/api/pay/callback.do";
        Integer payAmt = userRecharge.getPayAmt().multiply(new BigDecimal(100)).intValue();
        JSONObject paramMap = new JSONObject();
        paramMap.put("mchId", 20073);                               // 商户ID
        paramMap.put("appId", appId);                             // 应用ID,非必填
        paramMap.put("mchOrderNo", userRecharge.getOrderSn());     // 商户订单号
        paramMap.put("productId", productId);                       // 支付产品
        paramMap.put("amount", payAmt);                                // 支付金额,单位分
        paramMap.put("currency", "cny");                            // 币种, cny-人民币
        paramMap.put("subject", userRecharge.getUserId());
        paramMap.put("body", userRecharge.getNickName());
        paramMap.put("notifyUrl", fullDomain);                       // 回调URL

        String reqSign = PayDigestUtil.getSign(paramMap, key);
        paramMap.put("sign", reqSign);                              // 签名

        log.info("【sendThirdPayRequest】发起三方支付=>订单号：{}，请求参数：{}", userRecharge.getOrderSn(), JSONUtil.toJsonStr(paramMap));
        String response =
                HttpUtil.post("http://8.134.138.158:8020/api/pay/create_order", paramMap);
        log.info("【sendThirdPayRequest】三方支付返回结果=>订单号：{}，返回结果：{}", userRecharge.getOrderSn(), response);
        JSONObject jsonObject = JSONUtil.toBean(response, JSONObject.class);
        if ("SUCCESS".equals(jsonObject.getStr("retCode"))) {
            return jsonObject.getJSONObject("payParams").getStr("payUrl");
        }
        return null;
    }

    @Override
    public PayCallbackDto payCallback(Map<String, Object> callbackRequestParamMap, HttpServletRequest request,
                                      HttpServletResponse response) throws Exception {

        String mchOrderNo = String.valueOf(callbackRequestParamMap.get("mchOrderNo"));
        // 三方订单号！！
        String payOrderId = String.valueOf(callbackRequestParamMap.get("payOrderId"));
        String status = String.valueOf(callbackRequestParamMap.get("status"));
        // 请求⽀付下单时⾦额,保留两位小数
        String amount = String.valueOf(callbackRequestParamMap.get("amount"));
        // 金额转换为元，传过来是分
        amount = new BigDecimal(amount).divide(new BigDecimal(100)).toString();
        log.info(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，支付状态：{}，下单金额:{},实际支付金额：{}", mchOrderNo, payOrderId, status,
                amount, amount);
        UserRecharge userRecharge = this.userRechargeMapper.findUserRechargeByOrderSn(mchOrderNo);
        if (userRecharge == null) {
            log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，系统找不到对应的订单", mchOrderNo, payOrderId);
            response.getWriter().write("success");
            return PayCallbackDto.builder().paySuccess(false).orderNo(mchOrderNo).build();
        }
        if (Objects.equals(userRecharge.getOrderStatus(), NumberUtils.INTEGER_ONE)) {
            log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，已处理过，不再处理", mchOrderNo, payOrderId);
            response.getWriter().write("success");
            return PayCallbackDto.builder().paySuccess(false).orderNo(mchOrderNo).build();
        }

        User user = this.userMapper.selectByPrimaryKey(userRecharge.getUserId());
        if (user == null) {
            log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，系统找不到对应的用户", mchOrderNo, payOrderId);
            response.getWriter().write("success");
            return PayCallbackDto.builder().paySuccess(false).orderNo(mchOrderNo).build();
        }

        // if (!amount.equals(income)) {
        // log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，下单金额与实际付款金额不匹配，取实际付款金额", mchOrderNo, payOrderId);
        // userRecharge.setPayAmt(new BigDecimal(income));
        // }
        userRecharge.setPayTime(new Date());
        boolean isPaymentSuccess = false;
        switch (status) {
            case "-1":
                userRecharge.setOrderStatus(NumberUtils.INTEGER_TWO);
                log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，订单状态【失败】", mchOrderNo, payOrderId);
                break;
            case "2":
//            case "3":
                userRecharge.setOrderStatus(NumberUtils.INTEGER_ONE);
                log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，订单状态【支付成功】", mchOrderNo, payOrderId);
                response.getWriter().write("success");
                break;
            default:
                userRecharge.setOrderStatus(NumberUtils.INTEGER_TWO);
                log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，订单状态【失败】", mchOrderNo, payOrderId);
                break;
        }
        // ⽀付中⼼⽣成的订单号
        userRecharge.setPaySn(payOrderId);
        // 取支付的金额
        userRecharge.setPayAmt(new BigDecimal(amount));

        log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，开始修改订单状态", mchOrderNo, payOrderId);
        int updateCount = this.userRechargeMapper.updateByPrimaryKeySelective(userRecharge);
        if (NumberUtils.INTEGER_ONE.equals(userRecharge.getOrderStatus())) {
            BigDecimal totalAmt = user.getUserAmt().add(userRecharge.getPayAmt());
            user.setUserAmt(totalAmt);
            BigDecimal totalEnable = user.getEnableAmt().add(userRecharge.getPayAmt());
            user.setEnableAmt(totalEnable);
            int updateUserCount = this.userMapper.updateByPrimaryKeySelective(user);
            if (updateUserCount > 0) {
                isPaymentSuccess = true;
                log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，支付回调业务处理【成功】！！", mchOrderNo, payOrderId);
            }
        }
        response.getWriter().write("success");
        log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，支付回调业务处理流程【结束】！！", mchOrderNo, payOrderId);
        return PayCallbackDto.builder().paySuccess(isPaymentSuccess).orderNo(mchOrderNo).build();
    }
}
