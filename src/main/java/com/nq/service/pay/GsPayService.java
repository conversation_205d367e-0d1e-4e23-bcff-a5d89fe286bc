package com.nq.service.pay;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.nq.dao.UserMapper;
import com.nq.dao.UserRechargeMapper;
import com.nq.pojo.User;
import com.nq.pojo.UserRecharge;
import com.nq.utils.ip.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;

/**
 * 聚合支付通道实现
 *
 * <AUTHOR>
 * @since 2025/5/11 23:52
 */
//@Service
@Slf4j
public class GsPayService implements PayService {

    @Resource
    private UserRechargeMapper userRechargeMapper;

    @Resource
    private UserMapper userMapper;

    @Override
    public String sendThirdPayRequest(UserRecharge userRecharge, HttpServletRequest request) {
        String domain = request.getHeader("host");
        if (StrUtil.isEmpty(domain)) {
            domain = request.getServerName();
        }
        String callbackUrl = "https://" + domain + "/api/pay/callback.do";
        JSONObject params = new JSONObject();
        params.put("merchantId", "140");
        params.put("productId", "8001");
        params.put("orderNo", userRecharge.getOrderSn());
        params.put("currency", "cny");
        params.put("callbackUrl", callbackUrl);
        params.put("amount", userRecharge.getPayAmt().toString());
        params.put("clientIP", IpUtils.getIp(request));
        params.put("commodity", "asd");
        params.put("productDesc", "asd");

        // 生成签名前记录参数
        String signStr = generateSignString(params);
        log.info("【sendThirdPayRequest】发起三方支付=>订单号：{}，加密前字符串拼接：{}", userRecharge.getOrderSn(), signStr);

        String sign = generateSign(params);
        params.put("sign", sign);

        log.info("【sendThirdPayRequest】发起三方支付=>订单号：{}，请求参数：{}", userRecharge.getOrderSn(), params.toJSONString());
        String response = HttpUtil.post("http://************:8096/sfang-api/pay/createOrder", params.toJSONString());
        log.info("【sendThirdPayRequest】三方支付返回结果=>订单号：{}，返回结果：{}", userRecharge.getOrderSn(), response);

        JSONObject jsonObject = JSONObject.parseObject(response);
        if (jsonObject.getInteger("code").equals(0)) {
            JSONObject data = jsonObject.getJSONObject("data");
            userRecharge.setPaySn(data.getString("payNo"));
            userRechargeMapper.updateByPrimaryKeySelective(userRecharge);
            log.info("【sendThirdPayRequest】订单创建成功=>订单号：{}，三方订单号：{}", userRecharge.getOrderSn(), data.getString("payNo"));
            return data.getString("payParams");
        } else {
            log.error("【sendThirdPayRequest】订单创建失败=>订单号：{}，错误码：{}，错误信息：{}",
                    userRecharge.getOrderSn(), jsonObject.getInteger("code"), jsonObject.getString("message"));
        }
        return null;
    }

    @Override
    public PayCallbackDto payCallback(Map<String, Object> callbackRequestParamMap, HttpServletRequest request,
                                      HttpServletResponse response) throws Exception {

        // 获取回调参数
        String status = callbackRequestParamMap.get("status").toString();
        String orderNo = callbackRequestParamMap.get("orderNo").toString();
        String amount = callbackRequestParamMap.get("amount").toString();
        String payOrderId = callbackRequestParamMap.get("payNo") != null ?
                callbackRequestParamMap.get("payNo").toString() : orderNo;

        log.info(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，支付状态：{}，支付金额：{}",
                orderNo, payOrderId, status, amount);

        // 查询订单
        UserRecharge userRecharge = userRechargeMapper.findUserRechargeByOrderSn(orderNo);
        if (userRecharge == null) {
            log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，系统找不到对应的订单", orderNo, payOrderId);
            response.getWriter().write("fail");
            PayCallbackDto result = new PayCallbackDto();
            result.setPaySuccess(false);
            result.setOrderNo(orderNo);
            return result;
        }

        // 检查订单状态，避免重复处理
        if (NumberUtils.INTEGER_ONE.equals(userRecharge.getOrderStatus())) {
            log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，已处理过，不再处理", orderNo, payOrderId);
            response.getWriter().write("success");
            PayCallbackDto result = new PayCallbackDto();
            result.setPaySuccess(false);
            result.setOrderNo(orderNo);
            return result;
        }

        // 查询用户信息
        User user = userMapper.selectByPrimaryKey(userRecharge.getUserId());
        if (user == null) {
            log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，系统找不到对应的用户", orderNo, payOrderId);
            response.getWriter().write("fail");
            PayCallbackDto result = new PayCallbackDto();
            result.setPaySuccess(false);
            result.setOrderNo(orderNo);
            return result;
        }

        // 更新订单状态和支付时间
        userRecharge.setPayTime(new Date());
        userRecharge.setPaySn(payOrderId);
        userRecharge.setPayAmt(new BigDecimal(amount));

        // 根据支付状态处理订单
        if ("S".equals(status)) {
            // 支付成功
            userRecharge.setOrderStatus(NumberUtils.INTEGER_ONE);
            log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，订单状态【支付成功】", orderNo, payOrderId);
        } else {
            // 支付失败
            userRecharge.setOrderStatus(NumberUtils.INTEGER_TWO);
            log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，订单状态【失败】", orderNo, payOrderId);
        }

        log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，开始修改订单状态", orderNo, payOrderId);
        int updateCount = userRechargeMapper.updateByPrimaryKeySelective(userRecharge);

        if (updateCount > 0) {
            if (NumberUtils.INTEGER_ONE.equals(userRecharge.getOrderStatus())) {
                // 支付成功，更新用户资金
                BigDecimal totalAmt = user.getUserAmt().add(userRecharge.getPayAmt());
                user.setUserAmt(totalAmt);
                BigDecimal totalEnable = user.getEnableAmt().add(userRecharge.getPayAmt());
                user.setEnableAmt(totalEnable);
                int updateUserCount = userMapper.updateByPrimaryKeySelective(user);
                if (updateUserCount > 0) {
                    log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，支付回调业务处理【成功】！！", orderNo, payOrderId);
                    response.getWriter().write("success");
                    PayCallbackDto result = new PayCallbackDto();
                    result.setPaySuccess(true);
                    result.setOrderNo(orderNo);
                    return result;
                } else {
                    log.error(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，用户资金更新失败", orderNo, payOrderId);
                    response.getWriter().write("fail");
                    PayCallbackDto result = new PayCallbackDto();
                    result.setPaySuccess(false);
                    result.setOrderNo(orderNo);
                    return result;
                }
            } else {
                // 支付失败，但订单状态更新成功
                response.getWriter().write("success");
                PayCallbackDto result = new PayCallbackDto();
                result.setPaySuccess(false);
                result.setOrderNo(orderNo);
                return result;
            }
        } else {
            log.error(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，订单状态更新失败", orderNo, payOrderId);
            response.getWriter().write("fail");
            PayCallbackDto result = new PayCallbackDto();
            result.setPaySuccess(false);
            result.setOrderNo(orderNo);
            return result;
        }
    }

    /**
     * 生成签名字符串（用于日志记录）
     *
     * @param params 参数Map
     * @return 签名前的字符串
     */
    private String generateSignString(Map<String, Object> params) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : new TreeMap<>(params).entrySet()) {
            if (entry.getValue() != null && !"".equals(entry.getValue()) && !"sign".equals(entry.getKey())) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }
        String signStr = sb.toString();
        if (signStr.endsWith("&")) {
            signStr = signStr.substring(0, signStr.length() - 1);
        }
        signStr += "&key=MbeNgbgjYkbtor2H7G97uTujsu9RfAKDernMQsh7Up6KN2j8bjI2xOKP4S9z5jc0c6PVVeiG5N6F24tcBh0ZOQTiQEIRnF84YFhRWXmQuGPcAlTRNYxMtpIdf1uztJxS";
        return signStr;
    }

    /**
     * 生成签名
     *
     * @param params 参数Map
     * @return 签名字符串
     */
    private String generateSign(Map<String, Object> params) {
        String signStr = generateSignString(params);
        return SecureUtil.md5(signStr).toUpperCase();
    }

}
