package com.nq.service.pay;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.nq.dao.UserMapper;
import com.nq.dao.UserRechargeMapper;
import com.nq.pojo.User;
import com.nq.pojo.UserRecharge;
import com.nq.utils.ip.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;

/**
 * 聚合支付通道实现
 *
 * <AUTHOR>
 * @since 2025/5/11 23:52
 */
@Service
@Slf4j
public class GsPayService implements PayService {

    @Resource
    private UserRechargeMapper userRechargeMapper;

    @Resource
    private UserMapper userMapper;

    @Override
    public String sendThirdPayRequest(UserRecharge userRecharge, HttpServletRequest request) {
        String domain = request.getHeader("host");
        if (StrUtil.isEmpty(domain)) {
            domain = request.getServerName();
        }
        String callbackUrl = "https://" + domain + "/api/pay/callback.do";
        JSONObject params = new JSONObject();
        params.put("merchantId", "145");
        params.put("productId", "8001");
        params.put("orderNo", userRecharge.getOrderSn());
        params.put("currency", "cny");
        params.put("callbackUrl", callbackUrl);
        params.put("amount", userRecharge.getPayAmt().toString());
        params.put("clientIP", IpUtils.getIp(request));
        params.put("commodity", "asd");
        params.put("productDesc", "asd");
        String sign = generateSign(params);
        params.put("sign", sign);
        String response = HttpUtil.post("http://************:8096/sfang-api/pay/createOrder", params.toJSONString());
        JSONObject jsonObject = JSONObject.parseObject(response);
        if (jsonObject.getInteger("code").equals(0)) {
            JSONObject data = jsonObject.getJSONObject("data");
            userRecharge.setPaySn(data.getString("payNo"));
            userRechargeMapper.updateByPrimaryKeySelective(userRecharge);
            return data.getString("payParams");
        }
        return null;
    }

    @Override
    public PayCallbackDto payCallback(Map<String, Object> callbackRequestParamMap, HttpServletRequest request,
                                      HttpServletResponse response) throws Exception {
        log.info("【payCallback】支付回调=>回调参数：{}", JSONUtil.toJsonStr(callbackRequestParamMap));

        // 获取回调参数
        String status = callbackRequestParamMap.get("status").toString();
        String orderNo = callbackRequestParamMap.get("orderNo").toString();
        String amount = callbackRequestParamMap.get("amount").toString();

        // 查询订单
        UserRecharge userRecharge = userRechargeMapper.findUserRechargeByOrderSn(orderNo);
        if (userRecharge == null) {
            log.error("【payCallback】支付回调=>订单不存在，订单号：{}", orderNo);
            response.getWriter().write("order not found");
            PayCallbackDto result = new PayCallbackDto();
            result.setPaySuccess(false);
            result.setOrderNo(orderNo);
            return result;
        }

        // 检查订单状态，避免重复处理
        if (NumberUtils.INTEGER_ONE.equals(userRecharge.getOrderStatus())) {
            log.info("【payCallback】支付回调=>订单已处理，订单号：{}", orderNo);
            response.getWriter().write("ok");
            PayCallbackDto result = new PayCallbackDto();
            result.setPaySuccess(false);
            result.setOrderNo(orderNo);
            return result;
        }

        // 更新订单状态
        // 完成状态
        if (status.equals("S")) {
            // 设置为成功
            userRecharge.setOrderStatus(1);
            userRecharge.setPayTime(new Date());
//            userRecharge.setPaySn(payOrderId);
            // TODO 待确认 - 实际金额有可能扣除了手续费
            userRecharge.setPayAmt(new BigDecimal(amount));

            log.info("【payCallback】支付回调=>开始修改订单状态，订单号：{}", orderNo);
            int updateCount = userRechargeMapper.updateByPrimaryKeySelective(userRecharge);

            if (updateCount > 0) {
                // 更新用户余额
                User user = userMapper.selectByPrimaryKey(userRecharge.getUserId());
                if (user != null) {
                    BigDecimal totalAmt = user.getUserAmt().add(userRecharge.getPayAmt());
                    user.setUserAmt(totalAmt);
                    BigDecimal totalEnable = user.getEnableAmt().add(userRecharge.getPayAmt());
                    user.setEnableAmt(totalEnable);
                    int updateUserCount = userMapper.updateByPrimaryKeySelective(user);
                    if (updateUserCount > 0) {
                        log.info("【payCallback】支付回调=>用户余额更新成功，订单号：{}", orderNo);
                        response.getWriter().write("success");
                        PayCallbackDto result = new PayCallbackDto();
                        result.setPaySuccess(true);
                        result.setOrderNo(orderNo);
                        return result;
                    } else {
                        log.error("【payCallback】支付回调=>用户余额更新失败，订单号：{}", orderNo);
                        response.getWriter().write("fail");
                        PayCallbackDto result = new PayCallbackDto();
                        result.setPaySuccess(false);
                        result.setOrderNo(orderNo);
                        return result;
                    }
                } else {
                    log.error("【payCallback】支付回调=>订单状态更新失败，订单号：{}", orderNo);
                    response.getWriter().write("fail");
                    PayCallbackDto result = new PayCallbackDto();
                    result.setPaySuccess(false);
                    result.setOrderNo(orderNo);
                    return result;
                }
            } else {
                log.error("【payCallback】支付回调=>用户不存在，用户ID：{}", userRecharge.getUserId());
                response.getWriter().write("fail");
                PayCallbackDto result = new PayCallbackDto();
                result.setPaySuccess(false);
                result.setOrderNo(orderNo);
                return result;
            }
        } else { // 失败状态
            userRecharge.setOrderStatus(2); // 设置为失败
            int updateResult = userRechargeMapper.updateByPrimaryKeySelective(userRecharge);
            if (updateResult > 0) {
                response.getWriter().write("success");
                PayCallbackDto result = new PayCallbackDto();
                result.setPaySuccess(false);
                result.setOrderNo(orderNo);
                return result;
            } else {
                response.getWriter().write("fail");
                PayCallbackDto result = new PayCallbackDto();
                result.setPaySuccess(false);
                result.setOrderNo(orderNo);
                return result;
            }
        }
    }

    /**
     * 生成签名
     *
     * @param params 参数Map
     * @return 签名字符串
     */
    private String generateSign(Map<String, Object> params) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : new TreeMap<>(params).entrySet()) {
            if (entry.getValue() != null && !"".equals(entry.getValue()) && !"sign".equals(entry.getKey())) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }
        String signStr = sb.toString();
        if (signStr.endsWith("&")) {
            signStr = signStr.substring(0, signStr.length() - 1);
        }
        signStr += "&key=3f3KxjwDVQdTHV4y1SufIswlqNYD8s4X0IxD2hHmCkWXr0iPFbsUcvvYB05QvMt6LqPHdeSAkqebvjw73pX4oWDRvZYKuCQ1j8XOTpmBQCq3a8IqBYZJTOrjyojGWnBG";
        return SecureUtil.md5(signStr).toUpperCase();
    }

}
