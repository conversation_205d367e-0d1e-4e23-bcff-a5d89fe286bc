package com.nq.service.pay;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.nq.dao.UserMapper;
import com.nq.dao.UserRechargeMapper;
import com.nq.pojo.User;
import com.nq.pojo.UserRecharge;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 支付服务实现类
 * 实现与第三方支付平台的接口对接
 *
 * <AUTHOR>
 * @since 2025/5/19 21:11
 */
//@Service
@Slf4j
public class YyGggPayServiceImpl implements PayService {

    @Resource
    private UserRechargeMapper userRechargeMapper;

    @Resource
    private UserMapper userMapper;

    /**
     * 支付接口URL
     */
    private static final String PAY_API_URL = "https://jfjrapi.yyggg.xyz//api/pay/create_order";


    // 回调地址在请求时动态生成

    /**
     * 发送第三方支付请求
     *
     * @param userRecharge 用户充值信息
     * @param request      HTTP请求对象
     * @return 支付结果，通常是跳转URL或支付表单
     */
    @Override
    public String sendThirdPayRequest(UserRecharge userRecharge, HttpServletRequest request) {
        String orderSn = userRecharge.getOrderSn();
        log.info("【sendThirdPayRequest】发起支付请求=>订单号：{}，金额：{}", orderSn, userRecharge.getPayAmt());

        try {
            // 构建请求参数
            Map<String, Object> paramMap = buildRequestParams(userRecharge, request);

            // 生成签名
            String signStr = buildSignString(paramMap);
            log.info("【sendThirdPayRequest】发起支付请求=>订单号：{}，加密前字符串拼接：{}", orderSn, signStr);

            String sign = md5(signStr).toUpperCase();
            paramMap.put("sign", sign);

            log.info("【sendThirdPayRequest】发起支付请求=>订单号：{}，请求参数：{}", orderSn, JSON.toJSONString(paramMap));

            // 发送HTTP请求，直接传递参数Map
            String result = sendHttpRequest(PAY_API_URL, paramMap);
            log.info("【sendThirdPayRequest】支付返回结果=>订单号：{}，返回结果：{}", orderSn, result);

            // 解析响应结果
            JSONObject jsonResult = JSON.parseObject(result);
            String retCode = jsonResult.getString("retCode");

            if ("0".equals(retCode)) {
                // 验证返回签名
                if (verifyResponseSign(jsonResult)) {
                    // 根据支付方式处理返回结果
//                    String payMethod = jsonResult.getString("payMethod");
//                    log.info("【sendThirdPayRequest】支付返回支付方式=>订单号：{}，支付方式：{}", orderSn, payMethod);
//
//                    AtomicReference<String> payUrl = new AtomicReference<>("");
//
//                    if ("formJump".equals(payMethod)) {
//                        // 表单跳转方式
//                        String payJumpUrl = jsonResult.getString("payJumpUrl");
//                        if (StringUtils.isNotBlank(payJumpUrl)) {
//                            log.info("【sendThirdPayRequest】支付返回跳转地址=>订单号：{}，跳转地址：{}", orderSn, payJumpUrl);
//                            payUrl.set(payJumpUrl);
//                        } else {
//                            String formPayUrl = jsonResult.getString("payUrl");
//                            if (StringUtils.isNotBlank(formPayUrl)) {
//                                log.info("【sendThirdPayRequest】支付返回表单内容=>订单号：{}，表单内容长度：{}", orderSn, formPayUrl.length());
//                                payUrl.set(formPayUrl);
//                            }
//                        }
//                    } else if ("codeImg".equals(payMethod)) {
//                        // 二维码图片方式
//                        String codeImgUrl = jsonResult.getString("codeImgUrl");
//                        if (StringUtils.isNotBlank(codeImgUrl)) {
//                            log.info("【sendThirdPayRequest】支付返回二维码图片=>订单号：{}，二维码图片地址：{}", orderSn, codeImgUrl);
//                            payUrl.set(codeImgUrl);
//                        }
//                    } else if ("alipayApp".equals(payMethod) || "wxApp".equals(payMethod) || "wxJSApi".equals(payMethod)) {
//                        // APP支付方式
//                        JSONObject payParams = jsonResult.getJSONObject("payParams");
//                        if (payParams != null) {
//                            String appStr = payParams.getString("appStr");
//                            if (StringUtils.isNotBlank(appStr)) {
//                                log.info("【sendThirdPayRequest】支付返回APP参数=>订单号：{}，支付方式：{}", orderSn, payMethod);
//                                payUrl.set(appStr);
//                            }
//                        }
//                    }

                    String payUrl = jsonResult.getString("payJumpUrl");
                    if (StringUtils.isBlank(payUrl)) {
                        payUrl = jsonResult.getString("payUrl");
                    }

                    if (StringUtils.isNotBlank(payUrl)) {
                        // 替换返回的payUrl中的域名或IP为指定的域名
                        String newPayUrl = replaceUrlDomain(payUrl, "https://nhjkp.wshnoq.com");
                        log.info("【sendThirdPayRequest】支付请求成功=>订单号：{},原始URL：{}，替换后URL：{}", orderSn, payUrl, newPayUrl);

                        return newPayUrl;
                    } else {
                        log.error("【sendThirdPayRequest】支付返回结果解析失败=>订单号：{}，无法获取支付URL", orderSn);
                    }
                } else {
                    log.error("【sendThirdPayRequest】支付响应签名验证失败=>订单号：{}", orderSn);
                }
            } else {
                log.error("【sendThirdPayRequest】支付请求失败=>订单号：{}，错误码：{}，错误信息：{}",
                        orderSn, retCode, jsonResult.getString("retMsg"));
            }
        } catch (Exception e) {
            log.error("【sendThirdPayRequest】发送支付请求异常=>订单号：{}", orderSn, e);
        }

        log.warn("【sendThirdPayRequest】支付请求失败=>订单号：{}，返回空结果", orderSn);
        return "";
    }

    /**
     * 构建请求参数
     *
     * @param userRecharge 用户充值信息
     * @param request      HTTP请求对象
     * @return 请求参数Map
     */
    private Map<String, Object> buildRequestParams(UserRecharge userRecharge, HttpServletRequest request) {
        String orderSn = userRecharge.getOrderSn();
        log.info("【buildRequestParams】构建支付请求参数=>订单号：{}", orderSn);

        Map<String, Object> paramMap = new HashMap<>();

        // 必填参数
        paramMap.put("mchId", 20000006);  // 商户ID
//        paramMap.put("appId", appId);  // 应用ID
        paramMap.put("productId", "2003");  // 支付产品ID
        paramMap.put("mchOrderNo", orderSn);  // 商户订单号

        // 支付金额，单位分
        BigDecimal amountYuan = userRecharge.getPayAmt();
        int amountFen = amountYuan.multiply(new BigDecimal(100)).intValue();
        paramMap.put("amount", amountFen);
        log.info("【buildRequestParams】支付金额转换=>订单号：{}，元：{}，分：{}", orderSn, amountYuan, amountFen);

        paramMap.put("currency", "cny");  // 币种，人民币

        // 获取客户端IP
//        String clientIp = getClientIp(request);
//        if (StringUtils.isNotBlank(clientIp)) {
//            paramMap.put("clientIp", clientIp);
//            log.info("【buildRequestParams】客户端IP=>订单号：{}，IP：{}", orderSn, clientIp);
//        }

        // 设备信息
//        String userAgent = request.getHeader("User-Agent");
//        if (StringUtils.isNotBlank(userAgent)) {
//            paramMap.put("device", userAgent);
//        }

        // 回调地址
        // 获取当前域名
        String domain = request.getHeader("host");
        if (StringUtils.isEmpty(domain)) {
            domain = request.getServerName();
        }

        // 动态生成回调地址
        String notifyUrl = "https://" + domain + "/api/pay/callback.do";
        paramMap.put("notifyUrl", notifyUrl);  // 异步回调地址
        log.info("【buildRequestParams】异步回调地址=>订单号：{}，回调地址：{}", orderSn, notifyUrl);

        String returnUrl = "https://" + domain;
        paramMap.put("returnUrl", returnUrl);  // 同步回调地址
        log.info("【buildRequestParams】同步回调地址=>订单号：{}，回调地址：{}", orderSn, returnUrl);

        // 商品信息
        paramMap.put("subject", "账户充值");  // 商品主题
        paramMap.put("body", "用户ID:" + userRecharge.getUserId() + "充值" + amountYuan + "元");  // 商品描述

        // 请求时间，格式：yyyyMMddHHmmss
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String reqTime = sdf.format(new Date());
        paramMap.put("reqTime", reqTime);

        // 接口版本
        paramMap.put("version", "1.0");

        log.info("【buildRequestParams】构建支付请求参数完成=>订单号：{}，参数数量：{}", orderSn, paramMap.size());
        return paramMap;
    }

    /**
     * 构建签名字符串
     *
     * @param paramMap 参数Map
     * @return 签名原始字符串
     */
    private String buildSignString(Map<String, Object> paramMap) {
        // 按照参数名ASCII码从小到大排序
        Map<String, Object> sortedMap = new TreeMap<>(paramMap);

        // 拼接签名字符串
        StringBuilder stringBuilder = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedMap.entrySet()) {
            String paramName = entry.getKey();
            Object paramValue = entry.getValue();

            // 参数值为空不参与签名
            if (paramValue != null && StringUtils.isNotBlank(paramValue.toString())) {
                stringBuilder.append(paramName).append("=").append(paramValue).append("&");
            }
        }

        // 拼接key
        stringBuilder.append("key=").append("WRHQWQIY0KWFYJMXBL67OHII3U68EVNDUH7RMDH7QWH1BIPGMMVPXTUIC2N4H4RRIKBVER51OPF53TV3HWSHZRSZUX6RAEMGLM7VNLQZJX3USKLF5DR8YEOXTW3I19CU");

        return stringBuilder.toString();
    }

    /**
     * 生成签名
     *
     * @param paramMap 参数Map
     * @return 签名字符串
     */
    private String generateSign(Map<String, Object> paramMap) {
        String signStr = buildSignString(paramMap);
        return md5(signStr).toUpperCase();
    }

    /**
     * 验证响应签名
     *
     * @param jsonResult 响应JSON对象
     * @return 验证结果
     */
    private boolean verifyResponseSign(JSONObject jsonResult) {
        try {
            // 获取返回的签名
            String responseSign = jsonResult.getString("sign");
            if (StringUtils.isBlank(responseSign)) {
                log.error("【verifyResponseSign】支付响应签名为空");
                return false;
            }

            // 移除sign字段，重新计算签名
            Map<String, Object> paramMap = new HashMap<>();
            for (String key : jsonResult.keySet()) {
                if (!"sign".equals(key)) {
                    paramMap.put(key, jsonResult.get(key));
                }
            }

            // 构建签名字符串
            String signStr = buildSignString(paramMap);
            log.info("【verifyResponseSign】支付响应签名验证=>原始字符串：{}", signStr);

            // 生成签名
            String calculatedSign = md5(signStr).toUpperCase();
            log.info("【verifyResponseSign】支付响应签名验证=>计算签名：{}，响应签名：{}", calculatedSign, responseSign);

            // 比较签名
            boolean result = calculatedSign.equals(responseSign);
            if (!result) {
                log.error("【verifyResponseSign】支付响应签名验证失败=>计算签名：{}，响应签名：{}", calculatedSign, responseSign);
            }
            return result;
        } catch (Exception e) {
            log.error("【verifyResponseSign】验证响应签名异常", e);
            return false;
        }
    }

    /**
     * 发送HTTP请求
     * 使用application/x-www-form-urlencoded内容类型和UTF-8字符编码
     *
     * @param apiUrl   接口URL
     * @param paramMap 参数Map
     * @return 响应结果
     * @throws Exception 异常信息
     */
    private String sendHttpRequest(String apiUrl, Map<String, Object> paramMap) throws Exception {
        log.info("【sendHttpRequest】发送HTTP请求=>接口URL：{}", apiUrl);
        log.debug("【sendHttpRequest】发送HTTP请求参数=>参数数量：{}", paramMap.size());

        // 使用Hutool的HttpUtil发送POST请求，采用form表单提交方式
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
        headers.put("Accept", "application/json");

        // 使用form方式提交参数
        String responseBody = HttpUtil.createPost(apiUrl)
                .addHeaders(headers)
                .timeout(10000)
                .form(paramMap)  // 使用form方式提交参数
                .charset(java.nio.charset.StandardCharsets.UTF_8)  // 指定字符编码为UTF-8
                .execute()
                .body();

        log.debug("【sendHttpRequest】发送HTTP请求响应内容=>响应长度：{}", responseBody.length());
        log.debug("【sendHttpRequest】发送HTTP请求响应内容=>{}", responseBody);

        return responseBody;
    }

    /**
     * 获取客户端IP地址
     *
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 如果IP包含多个，取第一个
        if (StringUtils.isNotBlank(ip) && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }

    /**
     * MD5加密
     *
     * @param text 待加密文本
     * @return 加密后的字符串
     */
    private String md5(String text) {
        try {
            return SecureUtil.md5(text);
        } catch (Exception e) {
            log.error("【md5】MD5加密异常", e);
            return "";
        }
    }

    /**
     * 替换URL中的域名或IP
     * 只替换域名或IP部分，保持路径和查询参数不变
     *
     * @param originalUrl 原始URL
     * @param newDomain   新域名
     * @return 替换后的URL
     */
    private String replaceUrlDomain(String originalUrl, String newDomain) {
        try {
            if (StringUtils.isBlank(originalUrl)) {
                return originalUrl;
            }

            // 去除新域名结尾的斜杠
            if (newDomain.endsWith("/")) {
                newDomain = newDomain.substring(0, newDomain.length() - 1);
            }

            // 使用正则表达式替换URL中的域名或IP部分
            // 正则表达式匹配协议 + 域名/IP + 可选端口
            String regex = "^(https?://)[^/]+(/.*)$";
            String replacement = newDomain + "$2";

            // 如果原始URL不包含路径，需要特殊处理
            if (!originalUrl.matches(regex)) {
                // 如果原始URL只有域名没有路径
                regex = "^(https?://)[^/]+$";
                replacement = newDomain;
            }

            String result = originalUrl.replaceFirst(regex, replacement);
            log.info("【replaceUrlDomain】替换URL域名，原始URL：{}，替换后URL：{}", originalUrl, result);

            return result;
        } catch (Exception e) {
            log.error("【replaceUrlDomain】替换URL域名异常，原始URL：{}", originalUrl, e);
            return originalUrl;
        }
    }

    /**
     * 支付回调处理
     *
     * @param callbackRequestParamMap 回调请求参数Map
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws Exception 异常信息
     */
    /**
     * 支付回调处理
     * 处理支付平台的回调通知，验证签名并更新订单状态
     *
     * @param callbackRequestParamMap 回调请求参数Map
     * @param request                 HTTP请求对象
     * @param response                HTTP响应对象
     * @throws Exception 异常信息
     */
    @Override
    public void payCallback(Map<String, Object> callbackRequestParamMap, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 提取基本参数
        String mchOrderNo = String.valueOf(callbackRequestParamMap.get("mchOrderNo"));  // 商户订单号
        String payOrderId = String.valueOf(callbackRequestParamMap.get("payOrderId"));  // 支付平台订单号

        log.info(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，回调参数：{}",
                mchOrderNo, payOrderId, JSON.toJSONString(callbackRequestParamMap));

        try {
            // 验证签名
            String sign = (String) callbackRequestParamMap.get("sign");
            if (StringUtils.isBlank(sign)) {
                log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，签名为空",
                        mchOrderNo, payOrderId);
                response.getWriter().write("fail");
                return;
            }

            // 移除sign字段，重新计算签名
//            Map<String, Object> paramMap = new HashMap<>(callbackRequestParamMap);
//            paramMap.remove("sign");
//
//            // 构建签名字符串
//            String signStr = buildSignString(paramMap);
//            log.info(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，签名原始字符串：{}",
//                    mchOrderNo, payOrderId, signStr);
//
//            // 使用Hutool工具类进行MD5加密
//            String calculatedSign = SecureUtil.md5(signStr).toUpperCase();
//
//            // 比较签名
//            if (!calculatedSign.equals(sign)) {
//                log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，签名验证失败，计算签名：{}，回调签名：{}",
//                        mchOrderNo, payOrderId, calculatedSign, sign);
//                response.getWriter().write("fail");
//                return;
//            }

            // 获取必要参数
            String status = String.valueOf(callbackRequestParamMap.get("status"));  // 订单状态
            String amount = String.valueOf(callbackRequestParamMap.get("amount"));  // 请求支付金额，单位分
            String income = String.valueOf(callbackRequestParamMap.get("income"));  // 实际支付金额，单位分
            String channelOrderNo = String.valueOf(callbackRequestParamMap.get("channelOrderNo"));  // 三方支付渠道订单号
            String paySuccTime = String.valueOf(callbackRequestParamMap.get("paySuccTime"));  // 支付成功时间

            log.info(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，支付状态：{}，下单金额:{}分，实际支付金额:{}分",
                    mchOrderNo, payOrderId, status, amount, income);

            // 验证必要参数
            if (StringUtils.isBlank(mchOrderNo) || StringUtils.isBlank(status) || StringUtils.isBlank(amount) || StringUtils.isBlank(income)) {
                log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，参数不完整",
                        mchOrderNo, payOrderId);
                response.getWriter().write("fail");
                return;
            }

            // 查询订单信息
            UserRecharge userRecharge = this.userRechargeMapper.findUserRechargeByOrderSn(mchOrderNo);
            if (userRecharge == null) {
                log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，系统找不到对应的订单",
                        mchOrderNo, payOrderId);
                response.getWriter().write("success");
                return;
            }

            // 检查订单是否已处理
            if (Objects.equals(userRecharge.getOrderStatus(), NumberUtils.INTEGER_ONE)) {
                log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，已处理过，不再处理",
                        mchOrderNo, payOrderId);
                response.getWriter().write("success");
                return;
            }

            // 查询用户信息
            User user = this.userMapper.selectByPrimaryKey(userRecharge.getUserId());
            if (user == null) {
                log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，系统找不到对应的用户",
                        mchOrderNo, payOrderId);
                response.getWriter().write("success");
                return;
            }

            // 设置支付时间
            userRecharge.setPayTime(new Date());

            // 根据文档，支付状态为2或3表示支付成功
            // -2:订单已关闭,0-订单生成,1-支付中,2-支付成功,3-业务处理完成,4-已退款
            if ("2".equals(status) || "3".equals(status)) {
                // 设置订单状态为成功
                userRecharge.setOrderStatus(NumberUtils.INTEGER_ONE);
                log.info(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，订单状态【支付成功】",
                        mchOrderNo, payOrderId);
            } else {
                // 设置订单状态为失败
                userRecharge.setOrderStatus(NumberUtils.INTEGER_TWO);
                log.info(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，订单状态【失败】",
                        mchOrderNo, payOrderId);
                response.getWriter().write("success");
                return;
            }

            // 设置三方支付订单号
            userRecharge.setPaySn(payOrderId);

            // 使用实际支付金额（income）而非请求金额（amount）
            // 转换支付金额，从分转为元
            BigDecimal incomeYuan = new BigDecimal(income).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
            userRecharge.setPayAmt(incomeYuan);

            log.info(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，开始修改订单状态",
                    mchOrderNo, payOrderId);

            // 更新订单状态
            int updateCount = this.userRechargeMapper.updateByPrimaryKeySelective(userRecharge);

            // 如果订单状态为成功，则处理用户充值
            if (NumberUtils.INTEGER_ONE.equals(userRecharge.getOrderStatus())) {
                // 更新用户账户余额
                BigDecimal totalAmt = user.getUserAmt().add(userRecharge.getPayAmt());
                user.setUserAmt(totalAmt);
                BigDecimal totalEnable = user.getEnableAmt().add(userRecharge.getPayAmt());
                user.setEnableAmt(totalEnable);

                int updateUserCount = this.userMapper.updateByPrimaryKeySelective(user);
                if (updateUserCount > 0) {
                    log.info(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，支付回调业务处理【成功】！！",
                            mchOrderNo, payOrderId);
                }
            }

            // 根据文档要求，返回小写的"success"
            response.getWriter().write("success");
            log.info(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，支付回调业务处理流程【结束】！！",
                    mchOrderNo, payOrderId);

        } catch (Exception e) {
            log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，处理异常",
                    mchOrderNo, payOrderId, e);
            // 根据文档要求，异常时返回非"success"的字符串，支付平台会重新发起通知
            response.getWriter().write("fail");
        }
    }
}
