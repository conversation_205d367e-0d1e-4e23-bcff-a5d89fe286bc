package com.nq.service.pay;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.nq.dao.UserMapper;
import com.nq.dao.UserRechargeMapper;
import com.nq.pojo.User;
import com.nq.pojo.UserRecharge;
import com.nq.utils.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/28 16:15
 */
@Service
@Slf4j
public class Jj8PayServiceImpl implements PayService {

    @Resource
    private UserRechargeMapper userRechargeMapper;
    @Resource
    private UserMapper userMapper;

    @Override
    public String sendThirdPayRequest(UserRecharge userRecharge, HttpServletRequest request) {
        // userName test String 是 是 商户登录名
        // amount 10.00 String 是 是 订单金额 单位：元
        // 保留小数点后两位
        // payType quickpay String 否 是 充值类型，特定通道需要传，通常不用传递
        // outOrderId T1234567 String 是 是 商户订单号 需保持唯一
        // returnUrl http://www.xxxxxx.com/Notify_Url.jsp String 是 是 后端异步回调地址，用于通知支付结果
        // frontReturnUrl http://www.xxxxxx.com/index.jsp String 是 是 前端支付完成跳转地址，用于在支付结束后回到业务地址

        Map<String, Object> params = new LinkedHashMap<>();
        // BigInteger payAmount = userRecharge.getPayAmt().multiply(new BigDecimal(100)).toBigInteger();
        String domain = request.getHeader("host");
        if (StrUtil.isEmpty(domain)) {
            domain = request.getServerName();
        }
        String fullDomain = "https://" + domain + "/api/pay/callback.do";

        params.put("userName", "HLDQ023");
        params.put("amount", userRecharge.getPayAmt());
        // params.put("payType",payAmount );
        params.put("outOrderId", userRecharge.getOrderSn()); // 商户订单号
        params.put("returnUrl", fullDomain); // 支付结果前端跳转URL
        params.put("frontReturnUrl", domain); // 支付结果后台回调URL
        params.put("access_token", "H0TLTRZ0FN26620P888ZDDT2404F08");

        String signStr = MapUtil.mapToQueryString(params);
        log.info("【sendThirdPayRequest】发起三方支付=>订单号：{}，加密前字符串拼接：{}", userRecharge.getOrderSn(), signStr);
        String sign = SecureUtil.md5(signStr).toUpperCase();
        params.put("sign", sign);

        log.info("【sendThirdPayRequest】发起三方支付=>订单号：{}，请求参数：{}", userRecharge.getOrderSn(), JSONUtil.toJsonStr(params));
        String response =
                HttpUtil.post("https://r.jjpayapp.com/api/personnelfiles/preorder/addorder", JSONUtil.toJsonStr(params));
        log.info("【sendThirdPayRequest】三方支付返回结果=>订单号：{}，返回结果：{}", userRecharge.getOrderSn(), response);
        JSONObject jsonObject = JSONUtil.toBean(response, JSONObject.class);
        if ("1".equals(jsonObject.getStr("code"))) {
            return jsonObject.getStr("msg");
        }
        return null;
    }

    @Override
    public void payCallback(Map<String, Object> callbackRequestParamMap, HttpServletRequest request,
                            HttpServletResponse response) throws Exception {

        String mchOrderNo = String.valueOf(callbackRequestParamMap.get("outOrderId"));
        // 无三方订单号！！
        String payOrderId = String.valueOf(callbackRequestParamMap.get("outOrderId"));
        String status = String.valueOf(callbackRequestParamMap.get("status"));
        // 请求⽀付下单时⾦额,保留两位小数
        String amount = String.valueOf(callbackRequestParamMap.get("amount"));
        log.info(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，支付状态：{}，下单金额:{},实际支付金额：{}", mchOrderNo, payOrderId, status,
                amount, amount);
        UserRecharge userRecharge = this.userRechargeMapper.findUserRechargeByOrderSn(mchOrderNo);
        if (userRecharge == null) {
            log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，系统找不到对应的订单", mchOrderNo, payOrderId);
            response.getWriter().write("success");
            return;
        }
        if (Objects.equals(userRecharge.getOrderStatus(), NumberUtils.INTEGER_ONE)) {
            log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，已处理过，不再处理", mchOrderNo, payOrderId);
            response.getWriter().write("success");
            return;
        }

        User user = this.userMapper.selectByPrimaryKey(userRecharge.getUserId());
        if (user == null) {
            log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，系统找不到对应的用户", mchOrderNo, payOrderId);
            response.getWriter().write("success");
            return;
        }

        // if (!amount.equals(income)) {
        // log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，下单金额与实际付款金额不匹配，取实际付款金额", mchOrderNo, payOrderId);
        // userRecharge.setPayAmt(new BigDecimal(income));
        // }
        userRecharge.setPayTime(new Date());
        switch (status) {
            case "0":
                userRecharge.setOrderStatus(NumberUtils.INTEGER_TWO);
                log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，订单状态【失败】", mchOrderNo, payOrderId);
                break;
            case "1":
                userRecharge.setOrderStatus(NumberUtils.INTEGER_ONE);
                log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，订单状态【支付成功】", mchOrderNo, payOrderId);
                response.getWriter().write("success");
                break;
            default:
                userRecharge.setOrderStatus(NumberUtils.INTEGER_TWO);
                log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，订单状态【失败】", mchOrderNo, payOrderId);
                break;
        }
        // ⽀付中⼼⽣成的订单号
        userRecharge.setPaySn(payOrderId);
        // 取支付的金额
        userRecharge.setPayAmt(new BigDecimal(amount));

        log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，开始修改订单状态", mchOrderNo, payOrderId);
        int updateCount = this.userRechargeMapper.updateByPrimaryKeySelective(userRecharge);
        if (NumberUtils.INTEGER_ONE.equals(userRecharge.getOrderStatus())) {
            BigDecimal totalAmt = user.getUserAmt().add(userRecharge.getPayAmt());
            user.setUserAmt(totalAmt);
            BigDecimal totalEnable = user.getEnableAmt().add(userRecharge.getPayAmt());
            user.setEnableAmt(totalEnable);
            int updateUserCount = this.userMapper.updateByPrimaryKeySelective(user);
            if (updateUserCount > 0) {
                log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，支付回调业务处理【成功】！！", mchOrderNo, payOrderId);
            }
        }
        response.getWriter().write("success");
        log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，支付回调业务处理流程【结束】！！", mchOrderNo, payOrderId);
    }
}
