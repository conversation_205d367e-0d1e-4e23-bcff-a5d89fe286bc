package com.nq.service;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户业务服务接口
 * 处理复杂的用户业务逻辑
 * <AUTHOR>
 * @since 2025/1/15
 */
public interface UserBizService {
    
    /**
     * 管理员查询用户列表（包含当前代理下所有代理的用户）
     * @param realName 真实姓名
     * @param phone 手机号
     * @param agentId 代理ID
     * @param accountType 账户类型
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param status 状态
     * @param request 请求对象
     * @return 用户列表分页数据
     */
    ServerResponse<PageInfo> listByAdminWithSubAgents(String realName, String phone, Integer agentId, 
                                                     Integer accountType, int pageNum, int pageSize, 
                                                     Integer status, HttpServletRequest request);
}
