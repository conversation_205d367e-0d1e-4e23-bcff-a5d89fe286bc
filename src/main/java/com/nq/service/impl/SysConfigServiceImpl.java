package com.nq.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nq.dao.SysConfigMapper;
import com.nq.pojo.SysConfig;
import com.nq.service.SysConfigService;

import cn.hutool.core.collection.CollectionUtil;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/4/4 13:05
 */
@Service
@AllArgsConstructor
public class SysConfigServiceImpl implements SysConfigService {
    private final SysConfigMapper sysConfigMapper;

    @Override
    public SysConfig getSysConfig() {
        List<SysConfig> sysConfigList = sysConfigMapper.selectList(new LambdaQueryWrapper<>());
        if (CollectionUtil.isNotEmpty(sysConfigList)) {
            return sysConfigList.get(0);
        }
        return null;
    }

    @Override
    public int updateSysConfig(SysConfig sysConfig) {
        return sysConfigMapper.updateById(sysConfig);
    }
}
