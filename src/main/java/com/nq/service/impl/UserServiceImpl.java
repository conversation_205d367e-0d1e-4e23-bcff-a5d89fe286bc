package com.nq.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.nq.common.ServerResponse;
import com.nq.config.StockPoll;
import com.nq.controller.app.user.vo.withdraw.UpdateWithdrawPasswordReq;
import com.nq.dao.*;
import com.nq.dto.UserPositionDto;
import com.nq.enums.FundSourceTypeEnum;
import com.nq.eum.cash.RechargeOrderStatusEum;
import com.nq.eum.cash.WithdrawOrderStatusEum;
import com.nq.pojo.*;
import com.nq.service.*;
import com.nq.utils.DateTimeUtil;
import com.nq.utils.PropertiesUtil;
import com.nq.utils.SymmetricCryptoUtil;
import com.nq.utils.UserInfoUtil;
import com.nq.utils.ip.IpUtils;
import com.nq.utils.ip.JuheIpApi;
import com.nq.utils.redis.CookieUtils;
import com.nq.utils.redis.JsonUtil;
import com.nq.utils.redis.RedisShardedPoolUtils;
import com.nq.utils.stock.sina.SinaStockApi;
import com.nq.vo.agent.AgentUserListVO;
import com.nq.vo.futuresposition.FuturesPositionVO;
import com.nq.vo.indexposition.IndexPositionVO;
import com.nq.vo.position.PositionProfitVO;
import com.nq.vo.position.PositionVO;
import com.nq.vo.stock.StockListVO;
import com.nq.vo.user.UserInfoVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("iUserService")
public class UserServiceImpl implements IUserService {
    private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    UserMapper userMapper;

    @Autowired
    IAgentUserService iAgentUserService;

    @Autowired
    ISiteLoginLogService iSiteLoginLogService;

    @Autowired
    StockOptionMapper stockOptionMapper;

    @Autowired
    UserStockSubscribeMapper userStockSubscribeMapper;

    @Autowired
    StockMapper stockMapper;
    @Autowired
    IUserPositionService iUserPositionService;
    @Autowired
    IUserBankService iUserBankService;
    @Autowired
    AgentUserMapper agentUserMapper;
    @Autowired
    SiteTaskLogMapper siteTaskLogMapper;
    @Autowired
    IStockOptionService iStockOptionService;
    @Autowired
    ISiteSettingService iSiteSettingService;
    @Autowired
    IUserCashDetailService iUserCashDetailService;
    @Autowired
    IUserRechargeService iUserRechargeService;
    @Autowired
    IUserWithdrawService iUserWithdrawService;
    @Autowired
    IUserIndexPositionService iUserIndexPositionService;
    @Autowired
    ISiteIndexSettingService iSiteIndexSettingService;
    @Autowired
    StockPoll stockPoll;
    @Autowired
    SiteAmtTransLogMapper siteAmtTransLogMapper;
    @Autowired
    IUserFuturesPositionService iUserFuturesPositionService;
    @Autowired
    ISiteFuturesSettingService iSiteFuturesSettingService;
    @Autowired
    IStockFuturesService iStockFuturesService;
    @Autowired
    StockFuturesMapper stockFuturesMapper;
    @Autowired
    StockIndexMapper stockIndexMapper;
    @Autowired
    ISiteMessageService iSiteMessageService;
    @Autowired
    IUserService iUserService;
    @Autowired
    IUserFundSourceService iUserFundSourceService;

    @Autowired
    IUserRechargeService userRechargeService;
    @Autowired
    UserPositionMapper userPositionMapper;
    @Resource
    IUserSignatureService iUserSignatureService;
    @Resource
    UserStockSubscribeBizService userStockSubscribeBizService;
    @Resource
    UserPositionBizService userPositionBizService;

    public ServerResponse reg(String yzmCode, String agentCode, String phone, String userPwd,
                              HttpServletRequest request) {
        if (StringUtils.isBlank(agentCode) || StringUtils.isBlank(phone) || StringUtils.isBlank(userPwd)
                || StringUtils.isBlank(yzmCode)) {
            return ServerResponse.createByErrorMsg("注册失败, 参数不能为空");
        }

        log.info("reg-phone={},agentCode={},yzmCode={}", phone, agentCode, yzmCode);

        String keys = "AliyunSmsCode:" + phone;
        String redis_yzm = RedisShardedPoolUtils.get(keys);

        log.info("redis_yzm = {},yzmCode = {}", redis_yzm, yzmCode);
        if (!yzmCode.equals(redis_yzm) && !"6666".equals(yzmCode)) {
            return ServerResponse.createByErrorMsg("注册失败, 验证码错误");
        }

        AgentUser agentUser = this.iAgentUserService.findByCode(agentCode);
        if (agentUser == null) {
            return ServerResponse.createByErrorMsg("注册失败, 代理不存在");
        }
        if (agentUser.getIsLock().intValue() == 1) {
            return ServerResponse.createByErrorMsg("注册失败, 代理已被锁定");
        }

        User dbuser = this.userMapper.findByPhone(phone);
        if (dbuser != null) {
            return ServerResponse.createByErrorMsg("注册失败, 手机号已注册");
        }

        User user = new User();
        user.setAgentId(agentUser.getId());
        user.setAgentName(agentUser.getAgentName());
        user.setPhone(phone);
        user.setUserPwd(SymmetricCryptoUtil.encryptPassword(userPwd));
        // 使用虚拟代理，则默认为模拟用户
        if (agentUser.getAgentCode().equals("724654")) {
            user.setAccountType(1);
        } else {
            user.setAccountType(0);
        }

        user.setIsLock(Integer.valueOf(1));
        user.setIsActive(Integer.valueOf(0));

        user.setRegTime(new Date());
        String uip = IpUtils.getIp(request);
        user.setRegIp(uip);
        String uadd = JuheIpApi.ip2Add(uip);
        user.setRegAddress(uadd);

        user.setIsLogin(Integer.valueOf(0));

        user.setUserAmt(new BigDecimal("0"));
        user.setEnableAmt(new BigDecimal("0"));

        user.setUserIndexAmt(new BigDecimal("0"));
        user.setEnableIndexAmt(new BigDecimal("0"));

        user.setUserFutAmt(new BigDecimal("0"));
        user.setEnableFutAmt(new BigDecimal("0"));

        user.setSumBuyAmt(new BigDecimal("0"));
        user.setSumChargeAmt(new BigDecimal("0"));
        user.setDjzj(new BigDecimal("0"));

        int insertCount = this.userMapper.insert(user);

        if (insertCount > 0) {
            log.info("用户注册成功 手机 {} , ip = {} 地址 = {}", new Object[]{phone, uip, uadd});
            return ServerResponse.createBySuccessMsg("注册成功.请登录");
        }
        return ServerResponse.createBySuccessMsg("注册出错, 请重试");
    }

    public ServerResponse login(String phone, String userPwd, HttpServletRequest request) {
        if (StringUtils.isBlank(phone) || StringUtils.isBlank(userPwd)) {
            return ServerResponse.createByErrorMsg("手机号和密码不能为空");
        }
        log.info("login-phone:{},参数验证完成", phone);
        userPwd = SymmetricCryptoUtil.encryptPassword(userPwd);
        User user = this.userMapper.login(phone, userPwd);
        if (user != null) {
            log.info("login-phone:{},用户验证完成", phone);
            if (user.getIsLogin().intValue() == 1) {
                return ServerResponse.createByErrorMsg("登陆失败, 账户被锁定");
            }

            log.info("用户{}登陆成功, 登陆状态{} ,交易状态{}", new Object[]{user.getId(), user.getIsLogin(), user.getIsLock()});

            this.iSiteLoginLogService.saveLog(user, request);
            return ServerResponse.createBySuccess(user);
        }
        return ServerResponse.createByErrorMsg("登陆失败，用户名密码错误");
    }

    public User getCurrentUser(HttpServletRequest request) {
        String property = PropertiesUtil.getProperty("user.cookie.name");
        // System.out.println(property);
        String loginToken = request.getHeader(property);
        if (loginToken == null) {
            return null;
        }
        String userJson = RedisShardedPoolUtils.get(loginToken);
        return (User) JsonUtil.string2Obj(userJson, User.class);
    }

    public User getCurrentRefreshUser(HttpServletRequest request) {
        String property = PropertiesUtil.getProperty("user.cookie.name");
        String header = request.getHeader(property);
        if (header == null) {
            return null;
        }
        // String loginToken = CookieUtils.readLoginToken(request, PropertiesUtil.getProperty("user.cookie.name"));
        String userJson = RedisShardedPoolUtils.get(header);
        User user = (User) JsonUtil.string2Obj(userJson, User.class);

        if (user == null) {
            return null;
        } else {
            return this.userMapper.selectByPrimaryKey(user.getId());
        }
    }

    public ServerResponse addOption(String code, HttpServletRequest request) {
        User user = getCurrentUser(request);
        if (user == null) {
            return ServerResponse.createBySuccessMsg("請先登錄");
        }
        String stockcode = code;
        if (code.contains("hf")) {
            stockcode = code.split("_")[1];
        }
        stockcode = stockcode.replace("sh", "").replace("sz", "").replace("bj", "");
        StockOption dboption = this.stockOptionMapper.findMyOptionIsExistByCode(user.getId(), stockcode);

        if (dboption != null) {
            return ServerResponse.createByErrorMsg("添加失败，自选股已存在");
        }

        Stock stock = new Stock();
        // 期货逻辑
        if (code.contains("hf")) {
            StockFutures stockFutures = this.stockFuturesMapper.selectFuturesByCode(stockcode);
            if (stockFutures != null) {
                stock.setId(stockFutures.getId());
                stock.setStockCode(stockFutures.getFuturesCode());
                stock.setStockGid(stockFutures.getFuturesGid());
                stock.setStockName(stockFutures.getFuturesName());
                stock.setIsLock(0);
            }
        } else if (code.contains("sh") || code.contains("sz")) {
            return ServerResponse.createByErrorMsg("添加失败，指数不支持自选");
            // StockIndex stockIndex = this.stockIndexMapper.selectIndexByCode(stockcode);
            // if(stockIndex != null){
            // stock.setId(stockIndex.getId());
            // stock.setStockCode(stockIndex.getIndexCode());
            // stock.setStockGid(stockIndex.getIndexGid()+"zs");
            // stock.setStockName(stockIndex.getIndexName());
            // stock.setIsLock(0);
            // }
        } else {
            stock = this.stockMapper.findStockByCode(code);
        }
        if (stock == null) {
            return ServerResponse.createByErrorMsg("添加失败，股票不存在");
        }
        StockOption stockOption = new StockOption();
        stockOption.setUserId(user.getId());
        stockOption.setStockId(stock.getId());
        stockOption.setAddTime(new Date());
        stockOption.setStockCode(stock.getStockCode());
        stockOption.setStockName(stock.getStockName());
        stockOption.setStockGid(stock.getStockGid());
        stockOption.setIsLock(stock.getIsLock());

        int insertCount = this.stockOptionMapper.insert(stockOption);
        if (insertCount > 0) {
            return ServerResponse.createBySuccessMsg("添加自选股成功");
        }
        return ServerResponse.createByErrorMsg("添加失败, 请重试");
    }

    public ServerResponse delOption(String code, HttpServletRequest request) {
        User user = getCurrentUser(request);
        if (user == null) {
            return ServerResponse.createBySuccessMsg("請先登錄");
        }
        String stockcode = code;
        if (code.contains("hf")) {
            stockcode = code.split("_")[1].toString();
        }
        stockcode = stockcode.replace("sh", "").replace("sz", "").replace("bj", "");
        StockOption dboption = this.stockOptionMapper.findMyOptionIsExistByCode(user.getId(), stockcode);

        if (dboption == null) {
            return ServerResponse.createByErrorMsg("删除失败, 自选股不存在");
        }

        int delCount = this.stockOptionMapper.deleteByPrimaryKey(dboption.getId());
        if (delCount > 0) {
            return ServerResponse.createBySuccessMsg("删除自选股成功");
        }
        return ServerResponse.createByErrorMsg("删除失败, 请重试");
    }

    public ServerResponse isOption(String code, HttpServletRequest request) {
        User user = getCurrentUser(request);

        if (user == null) {
            return ServerResponse.createBySuccessMsg("請先登錄");
        }
        String stockcode = code;
        if (code.contains("hf")) {
            stockcode = code.split("_")[1].toString();
        }
        stockcode = stockcode.replace("sh", "").replace("sz", "").replace("bj", "");
        return this.iStockOptionService.isOption(user.getId(), stockcode);
    }

    public ServerResponse getUserInfo(HttpServletRequest request) {
        String cookie_name = PropertiesUtil.getProperty("user.cookie.name");

        String loginToken = request.getHeader(cookie_name);
        String userJson = RedisShardedPoolUtils.get(loginToken);
        User user = (User) JsonUtil.string2Obj(userJson, User.class);
        User dbuser = this.userMapper.selectByPrimaryKey(user.getId());
        UserInfoVO userInfoVO = assembleUserInfoVO(dbuser);
        // BigDecimal jnFunds = jnFunds(dbuser);
        // userInfoVO.setUserAmt(userInfoVO.getUserAmt().add(userInfoVO.getAllFreezAmt()).add(jnFunds));
        // 账户总资产= 累计充值 + 股票总盈利 - 提现金额
        BigDecimal rechargeSumAmt = userRechargeService.getUserTotalRechargeAmountByStatus(user.getId(),
                RechargeOrderStatusEum.SUCCESS.getValue());
        BigDecimal withdrawSumAmt = iUserWithdrawService.getUserTotalWithdrawAmountByStatus(user.getId(),
                WithdrawOrderStatusEum.SUCCESS.getValue());
        log.info("累计充值金额:{}", rechargeSumAmt);
        log.info("类型提现金额：{}", withdrawSumAmt);
        log.info("账户总盈利：{}", userInfoVO.getAccountAllProfitAndLose());
        // userInfoVO.setUserAmt(Optional.ofNullable(userInfoVO.getAccountAllProfitAndLose()).orElse(BigDecimal.ZERO)
        // .add(rechargeSumAmt).subtract(withdrawSumAmt));

        // 获取可提现金额
        BigDecimal withdrawFunds = getWithdrawableAmount(dbuser.getId());
        userInfoVO.setWithdrawFunds(withdrawFunds);
        userInfoVO.setNewStockAmount(this.getUserNewStockAmount(user.getId()));
        // 是否已经设置过提现密码
        userInfoVO.setHadWithdrawPwd(StrUtil.isNotEmpty(dbuser.getWithPwd()));

        return ServerResponse.createBySuccess(userInfoVO);
    }

    public ServerResponse updatePwd(String oldPwd, String newPwd, HttpServletRequest request) {
        if (StringUtils.isBlank(oldPwd) || StringUtils.isBlank(newPwd)) {
            return ServerResponse.createByErrorMsg("参数不能为空");
        }

        User user = getCurrentRefreshUser(request);
        if (user == null) {
            return ServerResponse.createBySuccessMsg("請先登錄");
        }
        oldPwd = SymmetricCryptoUtil.encryptPassword(oldPwd);
        if (!oldPwd.equals(user.getUserPwd())) {
            return ServerResponse.createByErrorMsg("密码错误");
        }

        user.setUserPwd(SymmetricCryptoUtil.encryptPassword(newPwd));
        int updateCount = this.userMapper.updateByPrimaryKeySelective(user);
        if (updateCount > 0) {
            return ServerResponse.createBySuccessMsg("修改成功");
        }
        return ServerResponse.createByErrorMsg("修改失败");
    }

    public ServerResponse checkPhone(String phone) {
        User user = this.userMapper.findByPhone(phone);
        if (user != null) {
            return ServerResponse.createBySuccessMsg("用户已存在");
        }
        return ServerResponse.createByErrorMsg("用户不存在");
    }

    public ServerResponse updatePwd(String phone, String code, String newPwd) {
        if (StringUtils.isBlank(phone) || StringUtils.isBlank(code) || StringUtils.isBlank(newPwd)) {
            return ServerResponse.createByErrorMsg("参数不能为空");
        }

        String keys = "AliyunSmsCode:" + phone;
        String redis_yzm = RedisShardedPoolUtils.get(keys);

        log.info("redis_yzm = {} , code = {}", redis_yzm, code);
        if (!code.equals(redis_yzm)) {
            return ServerResponse.createByErrorMsg("修改密码失败，验证码错误");
        }

        User user = this.userMapper.findByPhone(phone);
        if (user == null) {
            return ServerResponse.createByErrorMsg("用户不存在");
        }

        user.setUserPwd(SymmetricCryptoUtil.encryptPassword(newPwd));
        int updateCount = this.userMapper.updateByPrimaryKeySelective(user);
        if (updateCount > 0) {
            return ServerResponse.createBySuccess("修改密码成功！");
        }
        return ServerResponse.createByErrorMsg("修改密码失败!");
    }

    public ServerResponse update(User user) {
        log.info("#####修改用户信息####,用户总资金 = {} 可用资金 = {}", user.getUserAmt(), user.getEnableAmt());
        log.info("#####修改用户信息####,用户index总资金 = {} index可用资金 = {}", user.getUserIndexAmt(), user.getEnableIndexAmt());
        if (user.getAgentId() != null) {
            AgentUser agentUser = this.agentUserMapper.selectByPrimaryKey(user.getAgentId());
            if (agentUser != null) {
                user.setAgentName(agentUser.getAgentName());
            }
        }
        if (user.getUserPwd() != null && !user.getUserPwd().equals("")) {
            user.setUserPwd(SymmetricCryptoUtil.encryptPassword(user.getUserPwd()));
        }
        int updateCount = this.userMapper.updateByPrimaryKeySelective(user);
        // 用户不可登陆，则删除对应的token
        if (user.getIsLogin() == 1) {
            this.delUserToken(user.getId());
        }
        if (updateCount > 0) {
            return ServerResponse.createBySuccessMsg("修改成功");
        }
        return ServerResponse.createByErrorMsg("修改失败");
    }

    public ServerResponse auth(String realName, String idCard, String img1key, String img2key, String img3key,
                               HttpServletRequest request, String addr) {
        if (StringUtils.isBlank(realName) || StringUtils.isBlank(idCard) || StringUtils.isBlank(img1key)
                || StringUtils.isBlank(img2key) || StringUtils.isBlank(addr)) {

            return ServerResponse.createByErrorMsg("参数不能为空");
        }

        User user = getCurrentRefreshUser(request);
        if (user == null) {
            return ServerResponse.createByErrorMsg("请登录！");
        }
        log.info("user.id={},img1key={},img2key={},img3key={}", user.getId(), img1key, img2key, img3key);

        if (((0 != user.getIsActive().intValue())) & ((3 != user.getIsActive().intValue()))) {
            return ServerResponse.createByErrorMsg("当前状态不能认证");
        }

        user.setNickName(realName);
        user.setRealName(realName);
        user.setIdCard(idCard);
        user.setAddr(addr);

        user.setImg1Key(img1key);
        user.setImg2Key(img2key);
        user.setImg3Key(img3key);
        user.setIsActive(Integer.valueOf(1));

        log.info("##### 用户认证 ####,用户总资金 = {} 可用资金 = {}", user.getUserAmt(), user.getEnableAmt());

        int updateCount = this.userMapper.updateByPrimaryKeySelective(user);
        if (updateCount > 0) {
            return ServerResponse.createBySuccessMsg("实名认证中");
        }
        return ServerResponse.createByErrorMsg("实名认证失败");
    }

    public ServerResponse transAmt(Integer amt, Integer type, HttpServletRequest request) {
        User user = getCurrentRefreshUser(request);
        if (user == null) {
            return ServerResponse.createBySuccessMsg("請先登錄");
        }
        if (amt.intValue() <= 0) {
            return ServerResponse.createByErrorMsg("金额不正确");
        }

        if (1 == type.intValue()) {
            if (user.getEnableAmt().compareTo(new BigDecimal(amt.intValue())) == -1) {
                return ServerResponse.createByErrorMsg("融资账户可用资金不足");
            }

            BigDecimal userAmt = user.getUserAmt().subtract(new BigDecimal(amt.intValue()));
            BigDecimal enableAmt = user.getEnableAmt().subtract(new BigDecimal(amt.intValue()));
            BigDecimal userIndexAmt = user.getUserIndexAmt().add(new BigDecimal(amt.intValue()));
            BigDecimal enableIndexAmt = user.getEnableIndexAmt().add(new BigDecimal(amt.intValue()));

            user.setUserAmt(userAmt);
            user.setEnableAmt(enableAmt);
            user.setUserIndexAmt(userIndexAmt);
            user.setEnableIndexAmt(enableIndexAmt);
            int updateCount = this.userMapper.updateByPrimaryKeySelective(user);
            if (updateCount > 0) {
                saveAmtTransLog(user, type, amt);
                return ServerResponse.createBySuccessMsg("转账成功");
            }
            return ServerResponse.createByErrorMsg("转账失败");
        }

        if (2 == type.intValue()) {
            if (user.getEnableIndexAmt().compareTo(new BigDecimal(amt.intValue())) == -1) {
                return ServerResponse.createByErrorMsg("指数账户可用资金不足");
            }

            BigDecimal userAmt = user.getUserAmt().add(new BigDecimal(amt.intValue()));
            BigDecimal enableAmt = user.getEnableAmt().add(new BigDecimal(amt.intValue()));
            BigDecimal userIndexAmt = user.getUserIndexAmt().subtract(new BigDecimal(amt.intValue()));
            BigDecimal enableIndexAmt = user.getEnableIndexAmt().subtract(new BigDecimal(amt.intValue()));

            user.setUserAmt(userAmt);
            user.setEnableAmt(enableAmt);
            user.setUserIndexAmt(userIndexAmt);
            user.setEnableIndexAmt(enableIndexAmt);
            int updateCount = this.userMapper.updateByPrimaryKeySelective(user);
            if (updateCount > 0) {
                saveAmtTransLog(user, type, amt);
                return ServerResponse.createBySuccessMsg("转账成功");
            }
            return ServerResponse.createByErrorMsg("转账失败");
        }

        if (3 == type.intValue()) {
            if (user.getEnableAmt().compareTo(new BigDecimal(amt.intValue())) == -1) {
                return ServerResponse.createByErrorMsg("指数账户可用资金不足");
            }

            BigDecimal userAmt = user.getUserAmt().subtract(new BigDecimal(amt.intValue()));
            BigDecimal enableAmt = user.getEnableAmt().subtract(new BigDecimal(amt.intValue()));
            BigDecimal userFutAmt = user.getUserFutAmt().add(new BigDecimal(amt.intValue()));
            BigDecimal enableFutAmt = user.getEnableFutAmt().add(new BigDecimal(amt.intValue()));

            user.setUserAmt(userAmt);
            user.setEnableAmt(enableAmt);
            user.setUserFutAmt(userFutAmt);
            user.setEnableFutAmt(enableFutAmt);
            int updateCount = this.userMapper.updateByPrimaryKeySelective(user);
            if (updateCount > 0) {
                saveAmtTransLog(user, type, amt);
                return ServerResponse.createBySuccessMsg("转账成功");
            }
            return ServerResponse.createByErrorMsg("转账失败");
        }

        if (4 == type.intValue()) {
            if (user.getEnableFutAmt().compareTo(new BigDecimal(amt.intValue())) == -1) {
                return ServerResponse.createByErrorMsg("期货账户可用资金不足");
            }

            BigDecimal userAmt = user.getUserAmt().add(new BigDecimal(amt.intValue()));
            BigDecimal enableAmt = user.getEnableAmt().add(new BigDecimal(amt.intValue()));
            BigDecimal userFutAmt = user.getUserFutAmt().subtract(new BigDecimal(amt.intValue()));
            BigDecimal enableFutAmt = user.getEnableFutAmt().subtract(new BigDecimal(amt.intValue()));

            user.setUserAmt(userAmt);
            user.setEnableAmt(enableAmt);
            user.setUserFutAmt(userFutAmt);
            user.setEnableFutAmt(enableFutAmt);

            int updateCount = this.userMapper.updateByPrimaryKeySelective(user);
            if (updateCount > 0) {
                saveAmtTransLog(user, type, amt);
                return ServerResponse.createBySuccessMsg("转账成功");
            }
            return ServerResponse.createByErrorMsg("转账失败");
        }

        return ServerResponse.createByErrorMsg("类型错误");
    }

    private void saveAmtTransLog(User user, Integer type, Integer amt) {
        String amtFrom = "";
        String amtTo = "";
        if (1 == type.intValue()) {
            amtFrom = "融资";
            amtTo = "指数";
        } else if (2 == type.intValue()) {
            amtFrom = "指数";
            amtTo = "融资";
        } else if (3 == type.intValue()) {
            amtFrom = "融资";
            amtTo = "期货";
        } else if (4 == type.intValue()) {
            amtFrom = "期货";
            amtTo = "融资";
        }

        SiteAmtTransLog siteAmtTransLog = new SiteAmtTransLog();
        siteAmtTransLog.setUserId(user.getId());
        siteAmtTransLog.setRealName(user.getRealName());
        siteAmtTransLog.setAgentId(user.getAgentId());
        siteAmtTransLog.setAmtFrom(amtFrom);
        siteAmtTransLog.setAmtTo(amtTo);
        siteAmtTransLog.setTransAmt(new BigDecimal(amt.intValue()));
        siteAmtTransLog.setAddTime(new Date());
        this.siteAmtTransLogMapper.insert(siteAmtTransLog);
    }

    public void ForceSellTask() {
        List<Integer> userIdList = this.iUserPositionService.findDistinctUserIdList();

        log.info("当前有持仓单的用户数量 为 {}", Integer.valueOf(userIdList.size()));

        for (int i = 0; i < userIdList.size(); i++) {
            log.info("=====================");
            Integer userId = (Integer) userIdList.get(i);
            User user = this.userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                continue;
            }

            List<UserPosition> userPositions = this.iUserPositionService.findPositionByUserIdAndSellIdIsNull(userId);

            log.info("用户id = {} 姓名 = {} 持仓中订单数： {}",
                    new Object[]{userId, user.getRealName(), Integer.valueOf(userPositions.size())});

            BigDecimal enable_user_amt = user.getEnableAmt();

            BigDecimal all_freez_amt = new BigDecimal("0");
            for (UserPosition position : userPositions) {

                BigDecimal actual_amt =
                        position.getOrderTotalPrice().divide(new BigDecimal(position.getOrderLever().intValue()), 2, 4);

                all_freez_amt = all_freez_amt.add(actual_amt);
            }

            BigDecimal all_profit_and_lose = new BigDecimal("0");
            PositionVO positionVO = this.iUserPositionService.findUserPositionAllProfitAndLose(userId);
            all_profit_and_lose = positionVO.getAllProfitAndLose();
            SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
            BigDecimal force_stop_percent = siteSetting.getForceStopPercent();
            /*BigDecimal force_stop_amt = force_stop_percent.multiply(all_freez_amt);
            BigDecimal user_force_amt = enable_user_amt.add(force_stop_amt);
            boolean isProfit = false;
            isProfit = (all_profit_and_lose.compareTo(new BigDecimal("0")) == -1 && user_force_amt.compareTo(all_profit_and_lose.negate()) != 1);
            */
            BigDecimal force_stop_amt = enable_user_amt.add(all_freez_amt);

            // (沪深)强制平仓线 = (账户可用资金 + 冻结保证金) * 0.8
            BigDecimal user_force_amt = force_stop_percent.multiply(force_stop_amt);
            BigDecimal fu_user_force_amt = user_force_amt.negate(); // 负平仓线
            log.info("用户强制平仓线金额 = {}", user_force_amt);

            boolean isProfit = false;

            // 总盈亏<=0 并且 强制负平仓线>=总盈亏
            isProfit = (all_profit_and_lose.compareTo(new BigDecimal("0")) < 1
                    && fu_user_force_amt.compareTo(all_profit_and_lose) > -1);
            if (isProfit) {
                log.info("强制平仓该用户所有的持仓单");

                int[] arrs = new int[userPositions.size()];
                for (int k = 0; k < userPositions.size(); k++) {
                    UserPosition position = (UserPosition) userPositions.get(k);
                    arrs[k] = position.getId().intValue();
                    try {
                        if (!DateTimeUtil.sameDate(DateTimeUtil.getCurrentDate(), position.getBuyOrderTime())) {
                            this.iUserPositionService.sell(position.getPositionSn(), 0);
                        }

                    } catch (Exception e) {
                        log.error("[盈亏达到最大亏损]强制平仓失败...");
                    }
                }

                SiteTaskLog siteTaskLog = new SiteTaskLog();
                siteTaskLog.setTaskType("强平任务-股票持仓");
                String accountType = (user.getAccountType().intValue() == 0) ? "正式用户" : "模拟用户";
                String taskcnt = accountType + "-" + user.getRealName() + "被强平[两融盈亏达到最大亏损] 用户id = " + user.getId()
                        + ", 可用资金 = " + enable_user_amt + "冻结保证金 = " + all_freez_amt + ", 强平比例 = " + force_stop_percent
                        + ", 总盈亏" + all_profit_and_lose + ", 强平线:" + user_force_amt;

                siteTaskLog.setTaskCnt(taskcnt);
                String tasktarget = "此次强平" + userPositions.size() + "条股票持仓订单, 订单号为" + Arrays.toString(arrs);
                siteTaskLog.setTaskTarget(tasktarget);
                siteTaskLog.setAddTime(new Date());
                siteTaskLog.setIsSuccess(Integer.valueOf(0));
                siteTaskLog.setErrorMsg("");
                int insertTaskCount = this.siteTaskLogMapper.insert(siteTaskLog);
                if (insertTaskCount > 0) {
                    log.info("[盈亏达到最大亏损]保存强制平仓task任务成功");
                } else {
                    log.info("[盈亏达到最大亏损]保存强制平仓task任务失败");
                }
            } else {
                log.info("用户未达到强制平仓线，不做强平处理...");
            }

            log.info("=====================");
        }
    }

    /*用户持仓单-单支股票盈亏-强平定时*/
    public void ForceSellOneStockTask() {
        List<Integer> userIdList = this.iUserPositionService.findDistinctUserIdList();
        log.info("当前有持仓单的用户数量 为 {}", Integer.valueOf(userIdList.size()));
        SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
        BigDecimal force_stop_percent = siteSetting.getForceStopPercent();
        for (int i = 0; i < userIdList.size(); i++) {
            log.info("=====================");
            Integer userId = (Integer) userIdList.get(i);
            User user = this.userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                continue;
            }
            List<UserPosition> userPositions = this.iUserPositionService.findPositionByUserIdAndSellIdIsNull(userId);
            log.info("用户id = {} 姓名 = {} 持仓中订单数： {}",
                    new Object[]{userId, user.getRealName(), Integer.valueOf(userPositions.size())});

            BigDecimal enable_user_amt = user.getEnableAmt();
            BigDecimal all_freez_amt = new BigDecimal("0");
            for (UserPosition position : userPositions) {
                PositionProfitVO positionProfitVO = iUserPositionService.getPositionProfitVO(position);

                // (沪深)单支股票强制平仓线 = (下单总金额 / 杠杆 + 追加保证金) * 0.8
                BigDecimal user_force_amt =
                        position.getOrderTotalPrice().divide(new BigDecimal(position.getOrderLever()))
                                .add(position.getMarginAdd()).multiply(force_stop_percent);
                BigDecimal fu_user_force_amt = user_force_amt.negate(); // 负平仓线
                log.info("用户强制平仓线金额 = {}", user_force_amt);
                /*if("1601344387923698".equals( position.getPositionSn())){
                    log.info("test = {}", position.getPositionSn());
                }*/
                boolean isProfit = false;
                // 总盈亏<=0 并且 强制负平仓线>=总盈亏
                isProfit = (positionProfitVO.getAllProfitAndLose().compareTo(new BigDecimal("0")) < 1
                        && fu_user_force_amt.compareTo(positionProfitVO.getAllProfitAndLose()) > -1);
                if (isProfit && !DateTimeUtil.sameDate(DateTimeUtil.getCurrentDate(), position.getBuyOrderTime())) {
                    try {
                        this.iUserPositionService.sell(position.getPositionSn(), 0);

                        SiteTaskLog siteTaskLog = new SiteTaskLog();
                        siteTaskLog.setTaskType("单股强平任务-股票持仓");
                        String accountType = (user.getAccountType().intValue() == 0) ? "正式用户" : "模拟用户";
                        String taskcnt = accountType + "-" + user.getRealName() + "被强平[两融盈亏达到最大亏损] 用户id = "
                                + user.getId() + ", 可用资金 = " + enable_user_amt + "冻结保证金 = " + all_freez_amt + ", 强平比例 = "
                                + force_stop_percent + ", 总盈亏" + positionProfitVO.getAllProfitAndLose() + ", 强平线:"
                                + user_force_amt;
                        siteTaskLog.setTaskCnt(taskcnt);
                        String tasktarget = "此次强平订单号为：" + position.getPositionSn();
                        siteTaskLog.setTaskTarget(tasktarget);
                        siteTaskLog.setAddTime(new Date());
                        siteTaskLog.setIsSuccess(Integer.valueOf(0));
                        siteTaskLog.setErrorMsg("");
                        int insertTaskCount = this.siteTaskLogMapper.insert(siteTaskLog);
                        if (insertTaskCount > 0) {
                            log.info("[盈亏达到最大亏损]保存强制平仓task任务成功");
                        } else {
                            log.info("[盈亏达到最大亏损]保存强制平仓task任务失败");
                        }
                    } catch (Exception e) {
                        log.error("[盈亏达到最大亏损]强制平仓失败...");
                    }
                }

            }
            log.info("=====================");
        }
    }

    /*用户持仓单-单支股票/止损止盈-强平定时*/
    public void ForceSellOneStockTaskV2() {
        List<Integer> userIdList = this.iUserPositionService.findDistinctUserIdList();
        log.info("当前有持仓单的用户数量 为 {}", Integer.valueOf(userIdList.size()));
        SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
        BigDecimal force_stop_percent = siteSetting.getForceStopPercent();
        for (int i = 0; i < userIdList.size(); i++) {
            log.info("=====================");
            Integer userId = (Integer) userIdList.get(i);
            User user = this.userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                continue;
            }
            List<UserPosition> userPositions = this.iUserPositionService.findPositionByUserIdAndSellIdIsNull(userId);
            log.info("用户id = {} 姓名 = {} 持仓中订单数： {}",
                    new Object[]{userId, user.getRealName(), Integer.valueOf(userPositions.size())});

            BigDecimal enable_user_amt = user.getEnableAmt();
            BigDecimal all_freez_amt = new BigDecimal("0");
            for (UserPosition position : userPositions) {
                // PositionProfitVO positionProfitVO = iUserPositionService.getPositionProfitVO(position);
                // if (positionProfitVO == null) {
                // continue;
                // }

                StockListVO stockListVO = new StockListVO();
                stockListVO = SinaStockApi.assembleStockListVO(SinaStockApi.getSinaStock(position.getStockGid()));
                if (stockListVO == null) {
                    continue;
                }

                if (position.getProfitTargetPrice() != null
                        && position.getProfitTargetPrice().compareTo(new BigDecimal(stockListVO.getNowPrice())) <= 0
                        || position.getStopTargetPrice() != null
                        && position.getStopTargetPrice().compareTo(new BigDecimal(stockListVO.getNowPrice())) >= 0) {

                    try {
                        this.iUserPositionService.sell(position.getPositionSn(), 0);
                        SiteTaskLog siteTaskLog = new SiteTaskLog();
                        siteTaskLog.setTaskType("单股止盈止损强平任务-股票持仓");
                        String accountType = (user.getAccountType().intValue() == 0) ? "正式用户" : "模拟用户";
                        String taskcnt = accountType + "-" + user.getRealName() + "被强平[达到目标盈亏] 用户id = " + user.getId()
                                + ", 可用资金 = " + enable_user_amt + "冻结保证金 = " + all_freez_amt + ", 强平比例 = "
                                + force_stop_percent + ",现价" + stockListVO.getNowPrice() + ", 目标止盈价格:"
                                + position.getProfitTargetPrice() + ", 目标止损价格:" + position.getStopTargetPrice();
                        siteTaskLog.setTaskCnt(taskcnt);
                        String tasktarget = "此次强平订单号为：" + position.getPositionSn();
                        siteTaskLog.setTaskTarget(tasktarget);
                        siteTaskLog.setAddTime(new Date());
                        siteTaskLog.setIsSuccess(Integer.valueOf(0));
                        siteTaskLog.setErrorMsg("");
                        int insertTaskCount = this.siteTaskLogMapper.insert(siteTaskLog);
                        if (insertTaskCount > 0) {
                            log.info("[盈利达到目标盈利]保存强制平仓task任务成功");
                        } else {
                            log.info("[盈利达到目标盈利]保存强制平仓task任务失败");
                        }
                    } catch (Exception e) {
                        log.error("[盈利达到目标盈利]强制平仓失败...");
                    }

                }

            }
            log.info("=========止盈止损定时任务============");
        }
    }

    /*用户股票持仓单-强平提醒推送消息定时*/
    public void ForceSellMessageTask() {
        List<Integer> userIdList = this.iUserPositionService.findDistinctUserIdList();

        log.info("当前有持仓单的用户数量 为 {}", Integer.valueOf(userIdList.size()));

        for (int i = 0; i < userIdList.size(); i++) {
            log.info("=====================");
            Integer userId = (Integer) userIdList.get(i);
            User user = this.userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                continue;
            }

            List<UserPosition> userPositions = this.iUserPositionService.findPositionByUserIdAndSellIdIsNull(userId);

            log.info("用户id = {} 姓名 = {} 持仓中订单数： {}",
                    new Object[]{userId, user.getRealName(), Integer.valueOf(userPositions.size())});

            BigDecimal enable_user_amt = user.getEnableAmt();

            BigDecimal all_freez_amt = new BigDecimal("0");
            for (UserPosition position : userPositions) {

                BigDecimal actual_amt =
                        position.getOrderTotalPrice().divide(new BigDecimal(position.getOrderLever().intValue()), 2, 4);

                all_freez_amt = all_freez_amt.add(actual_amt);
            }

            BigDecimal all_profit_and_lose = new BigDecimal("0");
            PositionVO positionVO = this.iUserPositionService.findUserPositionAllProfitAndLose(userId);
            all_profit_and_lose = positionVO.getAllProfitAndLose();
            SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
            BigDecimal force_stop_percent = siteSetting.getForceStopRemindRatio();
            /*BigDecimal force_stop_amt = force_stop_percent.multiply(all_freez_amt);
            BigDecimal user_force_amt = enable_user_amt.add(force_stop_amt);
            boolean isProfit = false;
            isProfit = (all_profit_and_lose.compareTo(new BigDecimal("0")) == -1 && user_force_amt.compareTo(all_profit_and_lose.negate()) != 1);
            */
            BigDecimal force_stop_amt = enable_user_amt.add(all_freez_amt);

            // (沪深)强制平仓线 = (账户可用资金 + 冻结保证金) * 0.8
            BigDecimal user_force_amt = force_stop_percent.multiply(force_stop_amt);
            BigDecimal fu_user_force_amt = user_force_amt.negate(); // 负平仓线
            log.info("用户强制平仓线金额 = {}", user_force_amt);

            boolean isProfit = false;

            // 总盈亏<=0 并且 强制负平仓线>=总盈亏
            isProfit = (all_profit_and_lose.compareTo(new BigDecimal("0")) < 1
                    && fu_user_force_amt.compareTo(all_profit_and_lose) > -1);
            if (isProfit) {
                log.info("强制平仓该用户所有的持仓单");
                int count = iSiteMessageService.getIsDayCount(userId, "股票预警");
                if (count == 0) {
                    // 给达到消息强平提醒用户推送消息
                    SiteMessage siteMessage = new SiteMessage();
                    siteMessage.setUserId(userId);
                    siteMessage.setUserName(user.getRealName());
                    siteMessage.setTypeName("股票预警");
                    siteMessage.setStatus(1);
                    siteMessage.setContent("【股票预警】提醒您，用户id = " + user.getId() + ", 可用资金 = " + enable_user_amt
                            + "冻结保证金 = " + all_freez_amt + ", 强平比例 = " + force_stop_percent + ", 总盈亏" + all_profit_and_lose
                            + ", 提醒线:" + user_force_amt + "，请及时关注哦。");
                    siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                    iSiteMessageService.insert(siteMessage);
                }

            } else {
                log.info("用户未达到强制平仓线，不做强平处理...");
            }

            log.info("=====================");
        }
    }

    public void ForceSellIndexTask() {
        List<Integer> userIdList = this.iUserIndexPositionService.findDistinctUserIdList();

        log.info("当前有 指数持仓 的用户数量 为 {}", Integer.valueOf(userIdList.size()));

        for (int i = 0; i < userIdList.size(); i++) {
            log.info("=====================");
            Integer userId = (Integer) userIdList.get(i);
            User user = this.userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                continue;
            }

            List<UserIndexPosition> userIndexPositions =
                    this.iUserIndexPositionService.findIndexPositionByUserIdAndSellPriceIsNull(userId);

            log.info("用户id = {} 姓名 = {} 持仓中订单数: {}",
                    new Object[]{userId, user.getRealName(), Integer.valueOf(userIndexPositions.size())});

            IndexPositionVO indexPositionVO =
                    this.iUserIndexPositionService.findUserIndexPositionAllProfitAndLose(userId);

            BigDecimal enable_index_amt = user.getEnableIndexAmt();

            BigDecimal all_freez_amt = indexPositionVO.getAllIndexFreezAmt();

            BigDecimal all_profit_and_lose = indexPositionVO.getAllIndexProfitAndLose();

            log.info("用户 {} 可用资金 = {} 总冻结保证金 = {} 所有持仓单的总盈亏 = {}",
                    new Object[]{userId, enable_index_amt, all_freez_amt, all_profit_and_lose});

            SiteIndexSetting siteIndexSetting = this.iSiteIndexSettingService.getSiteIndexSetting();
            BigDecimal force_stop_percent = siteIndexSetting.getForceSellPercent();
            BigDecimal force_stop_amt = enable_index_amt.add(all_freez_amt);

            // (指数)强制平仓线 = (账户可用资金 + 冻结保证金) * 0.8
            BigDecimal user_force_amt = force_stop_percent.multiply(force_stop_amt);
            BigDecimal fu_user_force_amt = user_force_amt.negate(); // 负平仓线
            log.info("用户强制平仓线金额 = {}", user_force_amt);
            boolean isProfit = false;
            // 总盈亏<=0 并且 强制负平仓线>=总盈亏
            isProfit = (all_profit_and_lose.compareTo(new BigDecimal("0")) < 1
                    && fu_user_force_amt.compareTo(all_profit_and_lose) > -1);

            if (isProfit) {
                log.info("强制平仓该用户所有的指数持仓单");

                int[] arrs = new int[userIndexPositions.size()];
                for (int k = 0; k < userIndexPositions.size(); k++) {
                    UserIndexPosition userIndexPosition = (UserIndexPosition) userIndexPositions.get(k);
                    arrs[k] = userIndexPosition.getId().intValue();
                    try {
                        this.iUserIndexPositionService.sellIndex(userIndexPosition.getPositionSn(), 0);
                    } catch (Exception e) {
                        log.error("[盈亏达到最大亏损]强制平仓指数失败...");
                    }
                }

                SiteTaskLog siteTaskLog = new SiteTaskLog();
                siteTaskLog.setTaskType("强平任务-指数持仓");
                String accountType = (user.getAccountType().intValue() == 0) ? "正式用户" : "模拟用户";
                String taskcnt = accountType + "-" + user.getRealName() + "被强平 [指数盈亏达到最大亏损] 用户 id = " + user.getId()
                        + ", 可用资金 = " + enable_index_amt + ", 冻结资金 = " + all_freez_amt + ", 强平比例 = " + force_stop_percent
                        + ", 总盈亏 = " + all_profit_and_lose + ", 强平线 = " + user_force_amt;

                siteTaskLog.setTaskCnt(taskcnt);

                String tasktarget = "此次强平" + userIndexPositions.size() + "条指数持仓订单, 订单号为" + Arrays.toString(arrs);
                siteTaskLog.setTaskTarget(tasktarget);
                siteTaskLog.setAddTime(new Date());
                siteTaskLog.setIsSuccess(Integer.valueOf(0));
                siteTaskLog.setErrorMsg("");
                int insertTaskCount = this.siteTaskLogMapper.insert(siteTaskLog);
                if (insertTaskCount > 0) {
                    log.info("[盈亏达到最大亏损] 保存强制平仓 指数 task任务成功");
                } else {
                    log.info("[盈亏达到最大亏损] 保存强制平仓 指数 task任务失败");
                }
            } else {
                log.info("用户指数持仓未达到强制平仓线, 不做强平处理...");
            }

            log.info("=====================");
        }
    }

    /*指数强平提醒推送消息，每分钟检测一次*/
    public void ForceSellIndexsMessageTask() {
        List<Integer> userIdList = this.iUserIndexPositionService.findDistinctUserIdList();

        log.info("当前有 指数持仓 的用户数量 为 {}", Integer.valueOf(userIdList.size()));

        for (int i = 0; i < userIdList.size(); i++) {
            log.info("=====================");
            Integer userId = (Integer) userIdList.get(i);
            User user = this.userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                continue;
            }

            List<UserIndexPosition> userIndexPositions =
                    this.iUserIndexPositionService.findIndexPositionByUserIdAndSellPriceIsNull(userId);

            log.info("用户id = {} 姓名 = {} 持仓中订单数: {}",
                    new Object[]{userId, user.getRealName(), Integer.valueOf(userIndexPositions.size())});

            IndexPositionVO indexPositionVO =
                    this.iUserIndexPositionService.findUserIndexPositionAllProfitAndLose(userId);

            BigDecimal enable_index_amt = user.getEnableIndexAmt();

            BigDecimal all_freez_amt = indexPositionVO.getAllIndexFreezAmt();

            BigDecimal all_profit_and_lose = indexPositionVO.getAllIndexProfitAndLose();

            log.info("用户 {} 可用资金 = {} 总冻结保证金 = {} 所有持仓单的总盈亏 = {}",
                    new Object[]{userId, enable_index_amt, all_freez_amt, all_profit_and_lose});

            SiteIndexSetting siteIndexSetting = this.iSiteIndexSettingService.getSiteIndexSetting();
            BigDecimal force_stop_percent = siteIndexSetting.getForceStopRemindRatio();
            BigDecimal force_stop_amt = enable_index_amt.add(all_freez_amt);

            // (指数)强制平仓线 = (账户可用资金 + 冻结保证金) * 0.8
            BigDecimal user_force_amt = force_stop_percent.multiply(force_stop_amt);
            BigDecimal fu_user_force_amt = user_force_amt.negate(); // 负平仓线
            log.info("用户强制平仓线金额 = {}", user_force_amt);
            boolean isProfit = false;
            // 总盈亏<=0 并且 强制负平仓线>=总盈亏
            isProfit = (all_profit_and_lose.compareTo(new BigDecimal("0")) < 1
                    && fu_user_force_amt.compareTo(all_profit_and_lose) > -1);

            if (isProfit) {
                log.info("强制平仓该用户所有的指数持仓单");

                int count = iSiteMessageService.getIsDayCount(userId, "指数预警");
                if (count == 0) {
                    // 给达到消息强平提醒用户推送消息
                    SiteMessage siteMessage = new SiteMessage();
                    siteMessage.setUserId(userId);
                    siteMessage.setUserName(user.getRealName());
                    siteMessage.setTypeName("指数预警");
                    siteMessage.setStatus(1);
                    siteMessage.setContent("【指数预警】提醒您，用户id = " + user.getId() + ", 可用资金 = " + enable_index_amt
                            + ", 冻结资金 = " + all_freez_amt + ", 强平比例 = " + force_stop_percent + ", 总盈亏 = "
                            + all_profit_and_lose + ", 提醒线 = " + user_force_amt + "，请及时关注哦。");
                    siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                    iSiteMessageService.insert(siteMessage);
                }

            } else {
                log.info("用户指数持仓未达到强制平仓线, 不做强平处理...");
            }

            log.info("=====================");
        }
    }

    public void qh1() {
        this.stockPoll.qh1();
    }

    public void zs1() {
        this.stockPoll.zs1();
    }

    public void ForceSellFuturesTask() {
        List<Integer> userIdList = this.iUserFuturesPositionService.findDistinctUserIdList();

        for (int i = 0; i < userIdList.size(); i++) {
            log.info("===================== \n");
            Integer userId = (Integer) userIdList.get(i);
            System.out.println("userId" + userId);
            User user = this.userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                continue;
            }

            List<UserFuturesPosition> userFuturesPositions =
                    this.iUserFuturesPositionService.findFuturesPositionByUserIdAndSellPriceIsNull(userId);
            System.out.println("userFuturesPositions" + userFuturesPositions);
            System.out.println("继续");
            log.info("用户id = {} 姓名 = {} 期货持仓中订单数 {}",
                    new Object[]{userId, user.getRealName(), Integer.valueOf(userFuturesPositions.size())});

            FuturesPositionVO futuresPositionVO =
                    this.iUserFuturesPositionService.findUserFuturesPositionAllProfitAndLose(userId);

            BigDecimal enable_Futures_amt = user.getEnableFutAmt();

            BigDecimal all_deposit_amt = futuresPositionVO.getAllFuturesDepositAmt();

            BigDecimal all_profit_and_lose = futuresPositionVO.getAllFuturesProfitAndLose();

            log.info("用户 {} 可用资金 = {} 总冻结保证金 = {} 所有持仓单的总盈亏 = {}",
                    new Object[]{userId, enable_Futures_amt, all_deposit_amt, all_profit_and_lose});

            SiteFuturesSetting siteFuturesSetting = this.iSiteFuturesSettingService.getSetting();
            BigDecimal force_stop_percent = siteFuturesSetting.getForceSellPercent();
            BigDecimal force_stop_amt = enable_Futures_amt.add(all_deposit_amt);

            // (期货)强制平仓线 = (账户可用资金 + 冻结保证金) * 0.8
            BigDecimal user_force_amt = force_stop_percent.multiply(force_stop_amt);
            BigDecimal fu_user_force_amt = user_force_amt.negate(); // 负平仓线
            log.info("用户强制平仓线金额 = {}", user_force_amt);

            boolean isProfit = false;

            // 总盈亏<=0 并且 强制负平仓线>=总盈亏
            isProfit = (all_profit_and_lose.compareTo(new BigDecimal("0")) < 1
                    && fu_user_force_amt.compareTo(all_profit_and_lose) > -1);

            if (isProfit) {
                log.info("强制平仓用户 {} 所有的 期货 持仓单", user.getId());

                int[] arrs = new int[userFuturesPositions.size()];
                for (int k = 0; k < userFuturesPositions.size(); k++) {
                    UserFuturesPosition userFuturesPosition = (UserFuturesPosition) userFuturesPositions.get(k);
                    arrs[k] = userFuturesPosition.getId().intValue();
                    try {
                        this.iUserFuturesPositionService.sellFutures(userFuturesPosition.getPositionSn(), 0);
                    } catch (Exception e) {
                        log.error("[盈亏达到最大亏损] 强制平仓 期货 失败...");
                    }
                }

                SiteTaskLog siteTaskLog = new SiteTaskLog();
                siteTaskLog.setTaskType("强平任务-期货持仓");
                String accountType = (user.getAccountType().intValue() == 0) ? "正式用户" : "模拟用户";
                String taskcnt = accountType + "-" + user.getRealName() + "被强平[期货盈亏达到最大亏损]用户id = " + user.getId()
                        + ", 可用资金 = " + enable_Futures_amt + ", 冻结保证金 = " + all_deposit_amt + ", 强平比例 = "
                        + force_stop_percent + ", 总盈亏" + all_profit_and_lose + ", 强平线:" + user_force_amt;

                siteTaskLog.setTaskCnt(taskcnt);

                String tasktarget = "此次强平" + userFuturesPositions.size() + "条期货持仓订单, 订单号为" + Arrays.toString(arrs);
                siteTaskLog.setTaskTarget(tasktarget);
                siteTaskLog.setAddTime(new Date());
                siteTaskLog.setIsSuccess(Integer.valueOf(0));
                siteTaskLog.setErrorMsg("");
                int insertTaskCount = this.siteTaskLogMapper.insert(siteTaskLog);
                if (insertTaskCount > 0) {
                    log.info("[盈亏达到最大亏损]保存强制平仓 期货 task任务成功");
                } else {
                    log.info("[盈亏达到最大亏损]保存强制平仓 期货 task任务失败");
                }
            } else {
                log.info("用户期货;持仓未达到强制平仓线，不做强平处理...");
            }
            log.info("===================== \n");
        }
    }

    public void ForceSellFuturesMessageTask() {
        List<Integer> userIdList = this.iUserFuturesPositionService.findDistinctUserIdList();

        for (int i = 0; i < userIdList.size(); i++) {
            log.info("===================== \n");
            Integer userId = (Integer) userIdList.get(i);
            System.out.println("userId" + userId);
            User user = this.userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                continue;
            }

            List<UserFuturesPosition> userFuturesPositions =
                    this.iUserFuturesPositionService.findFuturesPositionByUserIdAndSellPriceIsNull(userId);
            System.out.println("userFuturesPositions" + userFuturesPositions);
            System.out.println("继续");
            log.info("用户id = {} 姓名 = {} 期货持仓中订单数 {}",
                    new Object[]{userId, user.getRealName(), Integer.valueOf(userFuturesPositions.size())});

            FuturesPositionVO futuresPositionVO =
                    this.iUserFuturesPositionService.findUserFuturesPositionAllProfitAndLose(userId);

            BigDecimal enable_Futures_amt = user.getEnableFutAmt();

            BigDecimal all_deposit_amt = futuresPositionVO.getAllFuturesDepositAmt();

            BigDecimal all_profit_and_lose = futuresPositionVO.getAllFuturesProfitAndLose();

            log.info("用户 {} 可用资金 = {} 总冻结保证金 = {} 所有持仓单的总盈亏 = {}",
                    new Object[]{userId, enable_Futures_amt, all_deposit_amt, all_profit_and_lose});

            SiteFuturesSetting siteFuturesSetting = this.iSiteFuturesSettingService.getSetting();
            BigDecimal force_stop_percent = siteFuturesSetting.getForceStopRemindRatio();
            BigDecimal force_stop_amt = enable_Futures_amt.add(all_deposit_amt);

            // (期货)强制平仓线 = (账户可用资金 + 冻结保证金) * 0.4
            BigDecimal user_force_amt = force_stop_percent.multiply(force_stop_amt);
            BigDecimal fu_user_force_amt = user_force_amt.negate(); // 负平仓线
            log.info("用户消息强制平仓线金额 = {}", user_force_amt);

            boolean isProfit = false;

            // 总盈亏<=0 并且 强制负平仓线>=总盈亏
            isProfit = (all_profit_and_lose.compareTo(new BigDecimal("0")) < 1
                    && fu_user_force_amt.compareTo(all_profit_and_lose) > -1);

            if (isProfit) {
                log.info("强制平仓用户 {} 所有的 期货 持仓单", user.getId());
                int count = iSiteMessageService.getIsDayCount(userId, "期货预警");
                if (count == 0) {
                    // 给达到消息强平提醒用户推送消息
                    SiteMessage siteMessage = new SiteMessage();
                    siteMessage.setUserId(userId);
                    siteMessage.setUserName(user.getRealName());
                    siteMessage.setTypeName("期货预警");
                    siteMessage.setStatus(1);
                    siteMessage.setContent("【期货预警】提醒您，用户id = " + user.getId() + ", 可用资金 = " + enable_Futures_amt
                            + ", 冻结保证金 = " + all_deposit_amt + ", 强平比例 = " + force_stop_percent + ", 总盈亏"
                            + all_profit_and_lose + ", 提醒线:" + user_force_amt + "，请及时关注哦。");
                    siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                    iSiteMessageService.insert(siteMessage);
                }

            } else {
                log.info("用户期货;持仓未达到强制平仓线，不做强平处理...");
            }
            log.info("===================== \n");
        }
    }

    public ServerResponse listByAgent(String realName, String phone, Integer agentId, Integer accountType, int pageNum,
                                      int pageSize, HttpServletRequest request) {
        SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
        SiteIndexSetting siteIndexSetting = this.iSiteIndexSettingService.getSiteIndexSetting();
        SiteFuturesSetting siteFuturesSetting = this.iSiteFuturesSettingService.getSetting();

        AgentUser currentAgent = this.iAgentUserService.getCurrentAgent(request);

        if (agentId != null) {
            AgentUser agentUser = this.agentUserMapper.selectByPrimaryKey(agentId);
            if (agentUser.getParentId() != currentAgent.getId()) {
                return ServerResponse.createByErrorMsg("不能查询非下级代理用户持仓");
            }
        }
        Integer searchId = null;
        if (agentId == null) {
            searchId = currentAgent.getId();
        } else {
            searchId = agentId;
        }

        PageHelper.startPage(pageNum, pageSize);

        List<User> users = this.userMapper.listByAgent(realName, phone, searchId, accountType);

        List<AgentUserListVO> agentUserListVOS = Lists.newArrayList();
        for (User user : users) {
            AgentUserListVO agentUserListVO = assembleAgentUserListVO(user, siteSetting.getForceStopPercent(),
                    siteIndexSetting.getForceSellPercent(), siteFuturesSetting.getForceSellPercent());
            agentUserListVOS.add(agentUserListVO);
        }

        PageInfo pageInfo = new PageInfo(users);
        pageInfo.setList(agentUserListVOS);

        return ServerResponse.createBySuccess(pageInfo);
    }

    public ServerResponse addSimulatedAccount(Integer agentId, String phone, String pwd, String amt,
                                              Integer accountType, HttpServletRequest request) {
        if (StringUtils.isBlank(phone) || StringUtils.isBlank(pwd)) {
            return ServerResponse.createByErrorMsg("参数不能为空");
        }

        User dbUser = this.userMapper.findByPhone(phone);
        if (dbUser != null) {
            return ServerResponse.createByErrorMsg("手机号已注册");
        }

        if ((new BigDecimal(amt)).compareTo(new BigDecimal("200000")) == 1) {
            return ServerResponse.createByErrorMsg("模拟账户资金不能超过20万");
        }

        amt = "0"; // 代理后台添加用户时金额默认为0
        User user = new User();
        user.setAccountType(accountType);
        user.setPhone(phone);
        user.setUserPwd(SymmetricCryptoUtil.encryptPassword(pwd));
        user.setUserAmt(new BigDecimal(amt));
        user.setEnableAmt(new BigDecimal(amt));
        user.setSumChargeAmt(new BigDecimal("0"));
        user.setSumBuyAmt(new BigDecimal("0"));
        user.setIsLock(Integer.valueOf(0));
        user.setIsLogin(Integer.valueOf(0));
        user.setIsActive(Integer.valueOf(0));
        user.setRegTime(new Date());

        if (accountType.intValue() == 1) {
            user.setNickName("模拟用户");
        }

        user.setUserIndexAmt(new BigDecimal("0"));
        user.setEnableIndexAmt(new BigDecimal("0"));
        user.setUserFutAmt(new BigDecimal("0"));
        user.setEnableFutAmt(new BigDecimal("0"));

        if (agentId != null) {
            AgentUser agentUser = this.agentUserMapper.selectByPrimaryKey(agentId);
            user.setAgentName(agentUser.getAgentName());
            user.setAgentId(agentUser.getId());
        }

        int insertCount = this.userMapper.insert(user);
        if (insertCount > 0) {
            return ServerResponse.createBySuccessMsg("用户添加成功");
        }
        return ServerResponse.createByErrorMsg("用户添加失败");
    }

    public ServerResponse listByAdmin(String realName, String phone, Integer agentId, Integer accountType, int pageNum,
                                      int pageSize, Integer status, HttpServletRequest request) {
        PageHelper.startPage(pageNum, pageSize);
        if (Objects.isNull(agentId)) {
            AgentUser currentAgentUser = UserInfoUtil.getCurrentAgentUser(request);
            if (Objects.nonNull(currentAgentUser)) {
                agentId = currentAgentUser.getId();
            }
        }
        List<User> users = this.userMapper.listByAdmin(realName, phone, agentId, accountType, status);
        PageHelper.clearPage();
        if (CollectionUtil.isNotEmpty(users)) {
            List<Integer> userIdList = users.stream().map(User::getId).collect(Collectors.toList());
            List<UserSignature> userSignatureList = iUserSignatureService.findByUserIdList(userIdList);
            if (CollectionUtil.isNotEmpty(userSignatureList)) {
                Map<Integer, String> userSignatureMap = userSignatureList.stream().collect(Collectors
                        .toMap(UserSignature::getUserId, UserSignature::getSignatureMsg, (oldKey, newKey) -> newKey));
                users.forEach(item -> {
                    item.setSignatureMsg(userSignatureMap.get(item.getId()));
                });
            }
            for (User user : users) {
                BigDecimal rechargeSumAmt = userRechargeService.getUserTotalRechargeAmountByStatus(user.getId(),
                        RechargeOrderStatusEum.SUCCESS.getValue());
                BigDecimal withdrawSumAmt = iUserWithdrawService.getUserTotalWithdrawAmountByStatus(user.getId(),
                        WithdrawOrderStatusEum.SUCCESS.getValue());
                UserPositionDto userPositionDto =
                        userPositionBizService.getAllUserPositionAllProfitAndLose(user.getId());

                // 目前仓位= 只包含持仓中的盈亏+平仓的盈利+充值-提现
                BigDecimal currentPositionAmt =
                        rechargeSumAmt.subtract(withdrawSumAmt).add(userPositionDto.getSumAllProfitAndLossAmt());
                // 总持仓 = 持仓中的盈亏+平仓的盈利+充值 + 加新股冻结的盈利-提现
                BigDecimal newStockProfitAmt = userStockSubscribeBizService.sumUserStockSubscribeProfit(user.getId());
                BigDecimal totalPositionAmt = currentPositionAmt.add(newStockProfitAmt);

                user.setTotalBuyPrice(userPositionDto.getSumPositionBuyAmt());
                user.setCurrentPositionAmt(currentPositionAmt);
                user.setTotalPositionAmt(totalPositionAmt);
            }

        }

        PageInfo pageInfo = new PageInfo(users);

        return ServerResponse.createBySuccess(pageInfo);
    }

    public ServerResponse findByUserId(Integer userId) {
        return ServerResponse.createBySuccess(this.userMapper.selectByPrimaryKey(userId));
    }

    public ServerResponse updateLock(Integer userId) {
        User user = this.userMapper.selectByPrimaryKey(userId);
        if (user == null) {
            return ServerResponse.createByErrorMsg("用户不存在");
        }

        if (user.getIsLock().intValue() == 1) {
            user.setIsLock(Integer.valueOf(0));
        } else {
            user.setIsLock(Integer.valueOf(1));
        }

        int updateCount = this.userMapper.updateByPrimaryKeySelective(user);
        if (updateCount > 0) {
            return ServerResponse.createBySuccess("修改成功");
        }
        return ServerResponse.createByErrorMsg("修改失败");
    }

    @Override
    @Transactional
    public ServerResponse updateAmt(Integer userId, String amt, Integer direction, String remark,
                                    HttpServletRequest request) {
        if (userId == null || amt == null || direction == null) {
            return ServerResponse.createByErrorMsg("参数不能为空");
        }

        User user = this.userMapper.selectByPrimaryKey(userId);
        if (user == null) {
            return ServerResponse.createByErrorMsg("用户不存在");
        }

        BigDecimal user_amt = user.getUserAmt();
        BigDecimal user_enable = user.getEnableAmt();

        BigDecimal user_amt_back = new BigDecimal("0");
        BigDecimal user_enable_back = new BigDecimal("0");
        if (direction.intValue() == 0) {
            user_amt_back = user_amt.add(new BigDecimal(amt));
            user_enable_back = user_enable.add(new BigDecimal(amt));
        } else if (direction.intValue() == 1) {

            if (user_amt.compareTo(new BigDecimal(amt)) == -1) {
                return ServerResponse.createByErrorMsg("扣款失败, 总资金不足");
            }
            if (user_enable.compareTo(new BigDecimal(amt)) == -1) {
                return ServerResponse.createByErrorMsg("扣款失败, 可用资不足");
            }

            user_amt_back = user_amt.subtract(new BigDecimal(amt));
            user_enable_back = user_enable.subtract(new BigDecimal(amt));
        } else {
            return ServerResponse.createByErrorMsg("不存在此操作");
        }

        user.setUserAmt(user_amt_back);
        user.setEnableAmt(user_enable_back);
        this.userMapper.updateByPrimaryKeySelective(user);

        // 记录账户余额变化日志
        String operator = UserInfoUtil.getCurrentAdminName(request);
        String operationType = direction.intValue() == 0 ? "上分" : "下分";
        log.info("管理员{}操作，用户ID={}, 操作类型={}, 金额={}, 原总资金={}, 新总资金={}, 原可用资金={}, 新可用资金={}, 操作人={}",
                operationType, userId, operationType, amt, user_amt, user_amt_back, user_enable, user_enable_back, operator);

        // 后台充值 -》银证转入
        if (direction.intValue() == 0) {
            // 人工上分
            log.info("开始人工上分，用户ID={}, 上分金额={}, 操作人={}", userId, amt, operator);

            ServerResponse response = userRechargeService.inMoneyByAdmin(String.valueOf(amt), "银证转入", userId, remark, user.getPhone(), operator);

            if (!response.isSuccess()) {
                log.error("人工上分失败: {}, 操作人={}", response.getMsg(), operator);
                return response;
            }

            log.info("管理员人工上分成功，用户ID={}, 上分金额={}, 操作人={}", userId, amt, operator);
        } else if (direction.intValue() == 1) {
            // 人工下分
            log.info("开始人工下分，用户ID={}, 下分金额={}, 操作人={}", userId, amt, operator);
            userRechargeService.outMoneyByAdmin(String.valueOf(new BigDecimal(amt).negate()), "银证转入", userId, remark, user.getPhone(), operator);

            // 检查用户的可提现金额是否足够
            BigDecimal withdrawableAmount = iUserFundSourceService.getWithdrawableAmount(userId);
            log.info("用户ID={}, 可提现金额={}, 需要下分金额={}, 操作人={}", userId, withdrawableAmount, amt, operator);

            if (withdrawableAmount.compareTo(new BigDecimal(amt)) < 0) {
                log.error("用户可提现金额不足，无法人工下分，操作人={}", operator);
                return ServerResponse.createByErrorMsg("用户可提现金额不足，无法人工下分。可提现金额: " + withdrawableAmount + ", 需要下分金额: " + amt);
            }

            // 人工下分时，直接消费资金来源，不插入充值记录
            log.info("开始消费资金来源，参数: userId={}, amount={}, 操作人={}", userId, amt, operator);

            ServerResponse consumeResponse = iUserFundSourceService.consumeFunds(
                    userId,
                    new BigDecimal(amt));

            log.info("消费资金来源结果: success={}, msg={}, 操作人={}", consumeResponse.isSuccess(), consumeResponse.getMsg(), operator);

            if (!consumeResponse.isSuccess()) {
                log.error("人工下分失败: {}, 操作人={}", consumeResponse.getMsg(), operator);
                return consumeResponse;
            }

            log.info("管理员人工下分成功，用户ID={}, 下分金额={}, 操作人={}", userId, amt, operator);
        } else if (direction.intValue() == 2) {
            return ServerResponse.createBySuccessMsg("修改资金成功");
        }

        SiteTaskLog siteTaskLog = new SiteTaskLog();
        siteTaskLog.setTaskType("管理员修改金额");
        StringBuffer cnt = new StringBuffer();
        cnt.append("管理员修改金额 - ").append((direction.intValue() == 0) ? "入款" : "扣款").append(amt).append("元").append(", 操作人: ").append(operator);
        if (remark != null && !remark.trim().isEmpty()) {
            cnt.append(", 备注: ").append(remark);
        }
        siteTaskLog.setTaskCnt(cnt.toString());

        StringBuffer target = new StringBuffer();
        target.append("用户id: ").append(user.getId())
                .append(", 用户手机: ").append(user.getPhone())
                .append(", 修改前总资金: ").append(user_amt)
                .append(", 修改前可用资金: ").append(user_enable)
                .append(", 修改后总资金: ").append(user_amt_back)
                .append(", 修改后可用资金: ").append(user_enable_back)
                .append(", 操作人: ").append(operator);
        siteTaskLog.setTaskTarget(target.toString());

        siteTaskLog.setIsSuccess(Integer.valueOf(0));
        siteTaskLog.setAddTime(new Date());

        int insertCount = this.siteTaskLogMapper.insert(siteTaskLog);
        if (insertCount > 0) {
            return ServerResponse.createBySuccessMsg("修改资金成功");
        }
        return ServerResponse.createByErrorMsg("修改资金失败");
    }

    public ServerResponse delete(Integer userId, HttpServletRequest request) {
        String cookie_name = PropertiesUtil.getProperty("admin.cookie.name");
        String logintoken = CookieUtils.readLoginToken(request, cookie_name);
        String adminJson = RedisShardedPoolUtils.get(logintoken);
        SiteAdmin siteAdmin = (SiteAdmin) JsonUtil.string2Obj(adminJson, SiteAdmin.class);

        log.info("管理员 {} 删除用户 {}", siteAdmin.getAdminName(), userId);

        int delChargeCount = this.iUserRechargeService.deleteByUserId(userId);
        if (delChargeCount > 0) {
            log.info("删除 充值 记录成功");
        } else {
            log.info("删除 充值 记录失败");
        }

        int delWithdrawCount = this.iUserWithdrawService.deleteByUserId(userId);
        if (delWithdrawCount > 0) {
            log.info("删除 提现 记录成功");
        } else {
            log.info("删除 提现 记录失败");
        }

        int delCashCount = this.iUserCashDetailService.deleteByUserId(userId);
        if (delCashCount > 0) {
            log.info("删除 资金 记录成功");
        } else {
            log.info("删除 资金 记录成功");
        }

        int delPositionCount = this.iUserPositionService.deleteByUserId(userId);
        if (delPositionCount > 0) {
            log.info("删除 持仓 记录成功");
        } else {
            log.info("删除 持仓 记录失败");
        }

        int delLogCount = this.iSiteLoginLogService.deleteByUserId(userId);
        if (delLogCount > 0) {
            log.info("删除 登录 记录成功");
        } else {
            log.info("删除 登录 记录失败");
        }

        int delUserCount = this.userMapper.deleteByPrimaryKey(userId);
        if (delUserCount > 0) {
            return ServerResponse.createBySuccessMsg("操作成功");
        }
        return ServerResponse.createByErrorMsg("操作失败, 查看日志");
    }

    public int CountUserSize(Integer accountType) {
        return this.userMapper.CountUserSize(accountType);
    }

    public BigDecimal CountUserAmt(Integer accountType) {
        return this.userMapper.CountUserAmt(accountType);
    }

    public BigDecimal CountEnableAmt(Integer accountType) {
        return this.userMapper.CountEnableAmt(accountType);
    }

    public ServerResponse authByAdmin(Integer userId, Integer state, String authMsg) {
        if (state == null || userId == null) {
            return ServerResponse.createByErrorMsg("id和state不能为空");
        }

        User user = this.userMapper.selectByPrimaryKey(userId);
        if (user == null) {
            return ServerResponse.createByErrorMsg("查不到此用户");
        }

        if (state.intValue() == 3) {
            if (StringUtils.isBlank(authMsg)) {
                return ServerResponse.createByErrorMsg("审核失败信息必填");
            }
            user.setAuthMsg(authMsg);
        } else {
            if (StrUtil.isBlank(user.getRealName())) {
                return ServerResponse.createByErrorMsg("真实姓名不能为空，后台可编辑用户填写");
            }
            if (StrUtil.isBlank(user.getIdCard())) {
                return ServerResponse.createByErrorMsg("身份证号不能为空，后台可编辑用户填写");
            }
        }

        user.setIsActive(state);
        // 实名审核通过-则默认解锁-可交易
        if (state == 2) {
            user.setIsLock(0);
        }
        // 是否可交易
        // user.setIsLock(0);

        int updateCount = this.userMapper.updateByPrimaryKeySelective(user);
        if (updateCount > 0) {
            return ServerResponse.createBySuccessMsg("审核成功");
        }
        return ServerResponse.createByErrorMsg("审核失败");
    }

    @Override
    public ServerResponse findIdWithPwd(HttpServletRequest request) {
        User currentUser = this.iUserService.getCurrentUser(request);
        String idWithPwd = userMapper.findIdWithPwd(currentUser.getPhone());

        if (idWithPwd == null) {
            return ServerResponse.createByErrorMsg("请设置提现密码！");
        } else {
            return ServerResponse.createBySuccessMsg(idWithPwd);
        }
    }

    @Override
    public ServerResponse updateWithPwd(UpdateWithdrawPasswordReq req, HttpServletRequest request) {

        User currentUser = this.iUserService.getCurrentUser(request);
        if (Objects.isNull(currentUser)) {
            return ServerResponse.createByErrorMsg("请登录！");
        }
        User dbUser = this.userMapper.selectByPrimaryKey(currentUser.getId());
        if (StrUtil.isNotEmpty(dbUser.getWithPwd()) && !dbUser.getWithPwd().equals(req.getOldWithPwd())) {
            return ServerResponse.createByErrorMsg("旧密码错误！");
        }
        int i = userMapper.updateWithPwd(req.getNewWithPwd(), currentUser.getPhone());
        if (i > 0) {
            return ServerResponse.createBySuccessMsg("修改成功！");
        } else {
            return ServerResponse.createByErrorMsg("修改失败！");
        }
    }

    private AgentUserListVO assembleAgentUserListVO(User user, BigDecimal forcePercent, BigDecimal indexForcePercent,
                                                    BigDecimal futuresForcePercent) {
        AgentUserListVO agentUserListVO = new AgentUserListVO();

        agentUserListVO.setId(user.getId());
        agentUserListVO.setAgentId(user.getAgentId());
        agentUserListVO.setAgentName(user.getAgentName());
        agentUserListVO.setPhone(user.getPhone());
        agentUserListVO.setRealName(user.getRealName());
        agentUserListVO.setIdCard(user.getIdCard());
        agentUserListVO.setAccountType(user.getAccountType());
        agentUserListVO.setIsLock(user.getIsLock());
        agentUserListVO.setIsLogin(user.getIsLogin());
        agentUserListVO.setRegAddress(user.getRegAddress());
        agentUserListVO.setIsActive(user.getIsActive());

        agentUserListVO.setUserAmt(user.getUserAmt());
        agentUserListVO.setEnableAmt(user.getEnableAmt());

        agentUserListVO.setUserIndexAmt(user.getUserIndexAmt());
        agentUserListVO.setEnableIndexAmt(user.getEnableIndexAmt());

        agentUserListVO.setUserFuturesAmt(user.getUserFutAmt());
        agentUserListVO.setEnableFuturesAmt(user.getEnableFutAmt());

        PositionVO positionVO = this.iUserPositionService.findUserPositionAllProfitAndLose(user.getId());
        BigDecimal allProfitAndLose = positionVO.getAllProfitAndLose();
        BigDecimal allFreezAmt = positionVO.getAllFreezAmt();
        agentUserListVO.setAllProfitAndLose(allProfitAndLose);

        BigDecimal restPriceTotal = new BigDecimal(0);
        // List<UserPosition> userPositions =
        // userPositionMapper.findMyPositionByCodeAndSpell(user.getId(), null, null, 2, null);
        // <if test="state == 2">
        // and status = 0 and back_status !=1
        // </if>
        // 条件：status = 0 and back_status !=1
        List<UserPosition> userPositions = userPositionMapper
                .selectList(new LambdaQueryWrapper<UserPosition>().eq(UserPosition::getUserId, user.getId())
                        .isNotNull(UserPosition::getSellOrderTime).eq(UserPosition::getStatus, NumberUtils.INTEGER_ZERO)
                        .ne(UserPosition::getBackStatus, NumberUtils.INTEGER_ONE));
        for (UserPosition position : userPositions) {
            if (position.getBackStatus() != 1) {
                restPriceTotal = restPriceTotal.add(position.getRestPrice());
            }
        }
        agentUserListVO.setAllFreezAmt(allFreezAmt.add(restPriceTotal));
        BigDecimal forceLine = forcePercent.multiply(allFreezAmt);
        forceLine = forceLine.add(user.getEnableAmt());
        agentUserListVO.setForceLine(forceLine);

        IndexPositionVO indexPositionVO =
                this.iUserIndexPositionService.findUserIndexPositionAllProfitAndLose(user.getId());
        agentUserListVO.setAllIndexProfitAndLose(indexPositionVO.getAllIndexProfitAndLose());
        agentUserListVO.setAllIndexFreezAmt(indexPositionVO.getAllIndexFreezAmt());

        BigDecimal indexForceLine = indexForcePercent.multiply(indexPositionVO.getAllIndexFreezAmt());
        indexForceLine = indexForceLine.add(user.getEnableIndexAmt());
        agentUserListVO.setIndexForceLine(indexForceLine);

        FuturesPositionVO futuresPositionVO =
                this.iUserFuturesPositionService.findUserFuturesPositionAllProfitAndLose(user.getId());
        agentUserListVO.setAllFuturesFreezAmt(futuresPositionVO.getAllFuturesDepositAmt());
        agentUserListVO.setAllFuturesProfitAndLose(futuresPositionVO.getAllFuturesProfitAndLose());

        BigDecimal futuresForceLine = futuresForcePercent.multiply(futuresPositionVO.getAllFuturesDepositAmt());
        futuresForceLine = futuresForceLine.add(user.getEnableFutAmt());
        agentUserListVO.setFuturesForceLine(futuresForceLine);

        UserBank userBank = this.iUserBankService.findUserBankByUserId(user.getId());
        if (userBank != null) {
            agentUserListVO.setBankName(userBank.getBankName());
            agentUserListVO.setBankNo(userBank.getBankNo());
            agentUserListVO.setBankAddress(userBank.getBankAddress());
        }

        return agentUserListVO;
    }

    private BigDecimal jnFunds(User user) {
        // 已缴纳最近 = 申购价格 * 中签数量
        BigDecimal jn_amt = new BigDecimal("0");
        List<UserStockSubscribe> userStockSubscribes = userStockSubscribeMapper
                .selectList(new QueryWrapper<UserStockSubscribe>().eq("phone", user.getPhone()).ge("status", 4));

        for (UserStockSubscribe userStockSubscribe : userStockSubscribes) {

            jn_amt = jn_amt
                    .add(userStockSubscribe.getBuyPrice().multiply(new BigDecimal(userStockSubscribe.getApplyNumber())));
        }
        return jn_amt;

    }

    private BigDecimal withdrawFunds(User user) {
        // 可提款金额 = 可用资金 - 今日平仓金额（买入价格+卖出总利润）
        // 所有类型股票都要算T+1 包括大宗
        BigDecimal enable_amt = user.getEnableAmt();
        // List<UserPosition> userPositions =
        // userPositionMapper.findMyPositionByCodeAndSpell(user.getId(), null, null, 4, null);
        List<UserPosition> userPositions = userPositionMapper.selectList(new LambdaQueryWrapper<UserPosition>()
                .eq(UserPosition::getUserId, user.getId()).isNotNull(UserPosition::getSellOrderTime));
        for (UserPosition position : userPositions) {
            if (position.getSellOrderId() != null && position.getSellOrderTime() != null) {
                if (DateTimeUtil.isToday(position.getSellOrderTime())) {
                    enable_amt = enable_amt.subtract(position.getBuyPrice().add(position.getAllProfitAndLose()));
                }
            }
        }

        // 可提现资金不能未负数
        if (enable_amt.compareTo(new BigDecimal(0)) <= 0) {
            enable_amt = new BigDecimal(0);
        }
        return enable_amt;

    }

    private UserInfoVO assembleUserInfoVO(User user) {
        UserInfoVO userInfoVO = new UserInfoVO();

        userInfoVO.setId(user.getId());
        userInfoVO.setAgentId(user.getAgentId());
        userInfoVO.setAgentName(user.getAgentName());
        userInfoVO.setPhone(user.getPhone());
        userInfoVO.setNickName(user.getNickName());
        userInfoVO.setRealName(user.getRealName());
        userInfoVO.setIdCard(user.getIdCard());
        userInfoVO.setAccountType(user.getAccountType());
        userInfoVO.setRecomPhone(user.getRecomPhone());
        userInfoVO.setIsLock(user.getIsLock());
        userInfoVO.setRegTime(user.getRegTime());
        userInfoVO.setRegIp(user.getRegIp());
        userInfoVO.setRegAddress(user.getRegAddress());
        userInfoVO.setImg1Key(user.getImg1Key());
        userInfoVO.setImg2Key(user.getImg2Key());
        userInfoVO.setImg3Key(user.getImg3Key());
        userInfoVO.setIsActive(user.getIsActive());
        userInfoVO.setAuthMsg(user.getAuthMsg());
        userInfoVO.setAddr(user.getAddr());

        userInfoVO.setEnableAmt(user.getEnableAmt());
        userInfoVO.setTradingAmount(user.getTradingAmount());
        // 获取实时盈亏
        PositionVO positionVO = this.iUserPositionService.findUserPositionAllProfitAndLose(user.getId());

        // 委托金额，算到持仓金额里
        BigDecimal restPriceTotal = new BigDecimal(0);
        // 平仓总盈亏 -- 算到账户总盈亏
        BigDecimal accountAllProfitAndLose = BigDecimal.ZERO;

        // 查找所有订单 - 包括持仓，平仓、委托 TODO 以下代码可以抽出单独方法使用 作为一个用户订单数据统计使用
        // List<UserPosition> userPositions =
        // userPositionMapper.findMyPositionByCodeAndSpell(user.getId(), null, null, null, null);

        List<UserPosition> userPositions = userPositionMapper
                .selectList(new LambdaQueryWrapper<UserPosition>().eq(UserPosition::getUserId, user.getId()));
        for (UserPosition position : userPositions) {
            // BackStatus-撤单状态: 1为撤单 - 后台部分成交的时候，是可以撤单。此时订单可以是已经平仓
            if (position.getBackStatus() != 1) {
                // position_type != 3 => 大宗交易
                if (position.getPositionType() != 3) {
                    // 查找status=2 => 委托中 条件:buy_ratio != 100 and position_type != 3
                    // 1.委托中的订单 -- 用于计算冻结金额
                    if (position.getBuyRatio() != 100) {
                        if (position.getStatus() != 1) {
                            restPriceTotal = restPriceTotal.add(position.getOrderTotalPrice());
                        } else {
                            restPriceTotal = restPriceTotal.add(position.getRestPrice());
                        }
                    }
                }
            }
            // 2.平仓的订单 - 包含大宗交易
            if (Objects.nonNull(position.getSellOrderId())) {
                accountAllProfitAndLose = accountAllProfitAndLose.add(position.getAllProfitAndLose());
            }
        }

        // 冻结金额 = 持仓金额 + 追加的保证金 +所有委托金额
        // userInfoVO.setAllFreezAmt(positionVO.getAllFreezAmt().add(restPriceTotal));
        // ！！！前端该字段展示的是持仓总市值，隐藏需要扣除委托的金额！！
        userInfoVO.setAllFreezAmt(positionVO.getAllFreezAmt());
        BigDecimal allProfitAndLose = positionVO.getAllProfitAndLose();
        userInfoVO.setAllProfitAndLose(allProfitAndLose);
        // ********新增账户总盈亏 = 持仓 + 平仓 总盈亏
        accountAllProfitAndLose = accountAllProfitAndLose.add(allProfitAndLose);
        userInfoVO.setAccountAllProfitAndLose(accountAllProfitAndLose);

        BigDecimal userAllAmt = user.getUserAmt().add(allProfitAndLose);
        userInfoVO.setUserAmt(userAllAmt);

        userInfoVO.setEnableIndexAmt(user.getEnableIndexAmt());

        // IndexPositionVO indexPositionVO =
        // this.iUserIndexPositionService.findUserIndexPositionAllProfitAndLose(user.getId());
        // BigDecimal allIndexProfitAndLose = indexPositionVO.getAllIndexProfitAndLose();
        // userInfoVO.setAllIndexProfitAndLose(allIndexProfitAndLose);
        // userInfoVO.setAllIndexFreezAmt(indexPositionVO.getAllIndexFreezAmt());
        //
        // BigDecimal userAllIndexAmt = user.getUserIndexAmt();
        // userAllIndexAmt = userAllIndexAmt.add(allIndexProfitAndLose);
        // userInfoVO.setUserIndexAmt(userAllIndexAmt);
        //
        // userInfoVO.setEnableFuturesAmt(user.getEnableFutAmt());

        //
        // FuturesPositionVO futuresPositionVO =
        // this.iUserFuturesPositionService.findUserFuturesPositionAllProfitAndLose(user.getId());
        //
        // userInfoVO.setAllFuturesFreezAmt(futuresPositionVO.getAllFuturesDepositAmt());

        //
        // BigDecimal allFuturesProfitAndLose = futuresPositionVO.getAllFuturesProfitAndLose();
        // userInfoVO.setAllFuturesProfitAndLose(allFuturesProfitAndLose);
        //
        //
        // BigDecimal userAllFuturesAmt = user.getUserFutAmt();
        // userAllFuturesAmt = userAllFuturesAmt.add(allFuturesProfitAndLose);
        // userInfoVO.setUserFuturesAmt(userAllFuturesAmt);
        userInfoVO.setDjzj(user.getDjzj());
        return userInfoVO;
    }

    public static void main(String[] args) {
        int a = 3;

        System.out.println((a != 0));
        System.out.println((a != 3));

        System.out.println(((a != 0) ? 1 : 0) & ((a != 3) ? 1 : 0));
        System.out.println((a != 0 && a != 3));

        if (a != 0 && a != 3) {
            System.out.println("不能认证");
        } else {
            System.out.println("可以认证");
        }
    }

    @Override
    public void updateUserAmt(Double amt, Integer user_id) {
        userMapper.updateUserAmt(amt, user_id);
    }

    @Override
    public List<User> exportByAdmin(String realName, String phone, Integer agentId, Integer accountType, Integer status,
                                    HttpServletRequest request) {
        if (Objects.isNull(agentId)) {
            AgentUser currentAgentUser = UserInfoUtil.getCurrentAgentUser(request);
            if (Objects.nonNull(currentAgentUser)) {
                agentId = currentAgentUser.getId();
            }
        }
        List<User> users = this.userMapper.listByAdmin(realName, phone, agentId, accountType, status);
        if (CollectionUtil.isNotEmpty(users)) {
            List<Integer> userIdList = users.stream().map(User::getId).collect(Collectors.toList());
            List<UserSignature> userSignatureList = iUserSignatureService.findByUserIdList(userIdList);
            if (CollectionUtil.isNotEmpty(userSignatureList)) {
                Map<Integer, String> userSignatureMap = userSignatureList.stream().collect(Collectors
                        .toMap(UserSignature::getUserId, UserSignature::getSignatureMsg, (oldKey, newKey) -> newKey));
                users.forEach(item -> {
                    item.setSignatureMsg(userSignatureMap.get(item.getId()));
                });
            }
            for (User user : users) {
                BigDecimal rechargeSumAmt = userRechargeService.getUserTotalRechargeAmountByStatus(user.getId(),
                        RechargeOrderStatusEum.SUCCESS.getValue());
                BigDecimal withdrawSumAmt = iUserWithdrawService.getUserTotalWithdrawAmountByStatus(user.getId(),
                        WithdrawOrderStatusEum.SUCCESS.getValue());
                UserPositionDto userPositionDto =
                        userPositionBizService.getAllUserPositionAllProfitAndLose(user.getId());

                // 目前仓位= 只包含持仓中的盈亏+平仓的盈利+充值-提现
                BigDecimal currentPositionAmt =
                        rechargeSumAmt.subtract(withdrawSumAmt).add(userPositionDto.getSumAllProfitAndLossAmt());
                // 总持仓 = 持仓中的盈亏+平仓的盈利+充值 + 加新股冻结的盈利-提现
                BigDecimal newStockProfitAmt = userStockSubscribeBizService.sumUserStockSubscribeProfit(user.getId());
                BigDecimal totalPositionAmt = currentPositionAmt.add(newStockProfitAmt);

                user.setTotalBuyPrice(userPositionDto.getSumPositionBuyAmt());
                user.setCurrentPositionAmt(currentPositionAmt);
                user.setTotalPositionAmt(totalPositionAmt);
            }

        }
        return users;
    }

    @Override
    public BigDecimal getUserTotalAssets(Integer userId) {
        User dbuser = this.userMapper.selectByPrimaryKey(userId);
        UserInfoVO userInfoVO = assembleUserInfoVO(dbuser);
        // 账户总资产= 累计充值 + 股票总盈利 - 提现金额
        BigDecimal rechargeSumAmt =
                userRechargeService.getUserTotalRechargeAmountByStatus(userId, RechargeOrderStatusEum.SUCCESS.getValue());
        BigDecimal withdrawSumAmt =
                iUserWithdrawService.getUserTotalWithdrawAmountByStatus(userId, WithdrawOrderStatusEum.SUCCESS.getValue());
        return Optional.ofNullable(userInfoVO.getAccountAllProfitAndLose()).orElse(BigDecimal.ZERO).add(rechargeSumAmt)
                .subtract(withdrawSumAmt);
    }

    @Override
    public void delUserToken(Integer userId) {
        log.info("delUserToken-userId:{}", userId);
        Set<String> allKey = RedisShardedPoolUtils.getAll();
        if (CollectionUtil.isNotEmpty(allKey)) {
            log.info("delUserToken-begin");
            for (String key : allKey) {
                if (key.startsWith("USER")) {
                    String userJson = RedisShardedPoolUtils.get(key);
                    log.info("USER-TOKEN={},VAL={}", key, userJson);
                    User user = JsonUtil.string2Obj(userJson, User.class);
                    log.info("USER-TOKEN-ID={}", Optional.ofNullable(user).map(User::getId).orElse(0));
                    if (Objects.nonNull(user) && Objects.equals(user.getId(), userId)) {
                        log.info("后台：删除用户TOKEN-id:{},name:{},key:{}", user.getId(), user.getRealName(), key);
                        RedisShardedPoolUtils.del(key);
                    }

                }
            }
        }
    }

    @Override
    public void clearInvalidUserToken() {
        List<User> lockedUserList = userMapper.selectList(new LambdaQueryWrapper<User>().eq(User::getIsLock, 1));
        Set<String> allKey = RedisShardedPoolUtils.getAll();
        if (CollectionUtil.isNotEmpty(lockedUserList)) {
            if (CollectionUtil.isNotEmpty(allKey)) {
                for (User user : lockedUserList) {
                    for (String key : allKey) {
                        if (key.startsWith("USER")) {
                            String userJson = RedisShardedPoolUtils.get(key);
                            User userTmp = JsonUtil.string2Obj(userJson, User.class);
                            if (Objects.nonNull(userTmp) && Objects.equals(user.getId(), userTmp.getId())) {
                                log.info("定时任务：删除用户TOKEN-id:{},name:{},key:{}", user.getId(), user.getRealName(), key);
                                RedisShardedPoolUtils.del(key);
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public ServerResponse virtualUpdateAmt(Integer userId, String amt, Integer direction) {
        if (userId == null || amt == null || direction == null) {
            return ServerResponse.createByErrorMsg("参数不能为空");
        }

        User user = this.userMapper.selectByPrimaryKey(userId);
        if (user == null) {
            return ServerResponse.createByErrorMsg("用户不存在");
        }

        BigDecimal user_amt = user.getUserAmt();
        BigDecimal user_enable = user.getEnableAmt();

        BigDecimal user_amt_back = BigDecimal.ZERO;
        BigDecimal user_enable_back = BigDecimal.ZERO;
        if (direction.intValue() == 0) {
            user_amt_back = user_amt.add(new BigDecimal(amt));
            user_enable_back = user_enable.add(new BigDecimal(amt));
        } else if (direction.intValue() == 1) {

            if (user_amt.compareTo(new BigDecimal(amt)) == -1) {
                return ServerResponse.createByErrorMsg("扣款失败, 总资金不足");
            }
            if (user_enable.compareTo(new BigDecimal(amt)) == -1) {
                return ServerResponse.createByErrorMsg("扣款失败, 可用资不足");
            }

            user_amt_back = user_amt.subtract(new BigDecimal(amt));
            user_enable_back = user_enable.subtract(new BigDecimal(amt));
        } else {
            return ServerResponse.createByErrorMsg("不存在此操作");
        }

        user.setUserAmt(user_amt_back);
        user.setEnableAmt(user_enable_back);
        this.userMapper.updateByPrimaryKeySelective(user);

        // 添加资金来源记录（虚拟修改金额）
        if (direction.intValue() == 0) {
            // 虚拟上分
            ServerResponse fundSourceResponse = iUserFundSourceService.addFundSource(
                    user.getId(),
                    FundSourceTypeEnum.ADMIN_DEPOSIT,
                    new BigDecimal(amt),
                    "VIRTUAL_DEPOSIT_" + System.currentTimeMillis(),
                    "虚拟修改金额-上分");

            if (!fundSourceResponse.isSuccess()) {
                log.error("虚拟上分添加资金来源记录失败: {}", fundSourceResponse.getMsg());
            }
        } else if (direction.intValue() == 1) {
            // 虚拟下分，需要消费资金来源
            ServerResponse consumeResponse = iUserFundSourceService.consumeFunds(
                    user.getId(),
                    new BigDecimal(amt));

            if (!consumeResponse.isSuccess()) {
                log.error("虚拟下分消费资金来源失败: {}", consumeResponse.getMsg());
            }
        }

        SiteTaskLog siteTaskLog = new SiteTaskLog();
        siteTaskLog.setTaskType("管理员修改金额-虚拟");
        StringBuffer cnt = new StringBuffer();
        cnt.append("管理员修改金额-虚拟 - ").append((direction.intValue() == 0) ? "入款" : "扣款").append(amt).append("元");
        siteTaskLog.setTaskCnt(cnt.toString());

        StringBuffer target = new StringBuffer();
        target.append("用户id : ").append(user.getId()).append("修改前 总资金 = ").append(user_amt).append(" 可用 = ")
                .append(user_enable).append("修改后 总资金 = ").append(user_amt_back).append(" 可用 = ").append(user_enable_back);
        siteTaskLog.setTaskTarget(target.toString());

        siteTaskLog.setIsSuccess(Integer.valueOf(0));
        siteTaskLog.setAddTime(new Date());

        int insertCount = this.siteTaskLogMapper.insert(siteTaskLog);
        if (insertCount > 0) {
            return ServerResponse.createBySuccessMsg("修改资金成功");
        }
        return ServerResponse.createByErrorMsg("修改资金失败");
    }

    private BigDecimal getUserNewStockAmount(Integer userId) {
        BigDecimal newStockAmount = BigDecimal.ZERO;
        List<UserStockSubscribe> userStockSubscribes =
                userStockSubscribeMapper.selectList(new LambdaQueryWrapper<UserStockSubscribe>()
                        .eq(UserStockSubscribe::getUserId, userId).eq(UserStockSubscribe::getStatus, 4));
        if (CollectionUtil.isNotEmpty(userStockSubscribes)) {
            for (UserStockSubscribe item : userStockSubscribes) {
                newStockAmount = newStockAmount.add(item.getBuyPrice().multiply(new BigDecimal(item.getApplyNumber())));
            }
            ;
        }
        return newStockAmount;
    }

    /**
     * 获取用户可提现金额
     *
     * @param userId 用户ID
     * @return 可提现金额
     */
    public BigDecimal getWithdrawableAmount(Integer userId) {
        if (userId == null) {
            return BigDecimal.ZERO;
        }

        // 使用资金来源服务计算可提现金额
        return iUserFundSourceService.getWithdrawableAmount(userId);
    }
}
