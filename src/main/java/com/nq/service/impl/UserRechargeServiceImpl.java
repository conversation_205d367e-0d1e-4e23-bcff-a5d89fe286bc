package com.nq.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dao.AgentUserMapper;
import com.nq.dao.UserCashDetailMapper;
import com.nq.dao.UserMapper;
import com.nq.dao.UserRechargeMapper;
import com.nq.pojo.*;
import com.nq.service.*;
import com.nq.service.pay.PayService;
import com.nq.utils.DateTimeUtil;
import com.nq.utils.KeyUtils;
import com.nq.utils.MapUtil;
import com.nq.utils.ip.IpUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service("iUserRechargeService")
public class UserRechargeServiceImpl implements IUserRechargeService {
    private static final Logger log = LoggerFactory.getLogger(UserRechargeServiceImpl.class);

    @Autowired
    UserRechargeMapper userRechargeMapper;

    @Autowired
    IUserService iUserService;

    @Autowired
    UserMapper userMapper;

    @Autowired
    IAgentUserService iAgentUserService;
    @Autowired
    AgentUserMapper agentUserMapper;
    @Autowired
    ISiteSettingService iSiteSettingService;
    @Autowired
    UserCashDetailMapper userCashDetailMapper;
    @Autowired
    ISiteInfoService iSiteInfoService;
    @Resource
    private IAgentUserService agentUserService;
    @Resource
    private ISiteProductService iSiteProductService;
    @Resource
    private PayService payService;

    public ServerResponse checkInMoney(int maxOrder, Integer userId) {
        int count = this.userRechargeMapper.checkInMoney(0, userId);

        if (count > maxOrder) {
            return ServerResponse.createByErrorMsg("一小时内只能发起" + maxOrder + "次入金");
        }
        return ServerResponse.createBySuccess();
    }

    public ServerResponse inMoney(String amt, String payType, String password, HttpServletRequest request) {
        if (StringUtils.isBlank(amt)) {
            return ServerResponse.createByErrorMsg("参数不能为空");
        }
        // SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
        // if (siteSetting == null) {
        // return ServerResponse.createByErrorMsg("设置set未初始化");
        // }

        // if ((new BigDecimal(siteSetting.getChargeMinAmt() + "")).compareTo(new BigDecimal(amt)) == 1) {
        // return ServerResponse.createByErrorMsg("充值金额不得低于" + siteSetting.getChargeMinAmt() + "元");
        // }

        // SiteInfo siteInfo = null;
        // ServerResponse serverResponseInfo = this.iSiteInfoService.getInfo(request);
        // if (serverResponseInfo.isSuccess()) {
        // siteInfo = (SiteInfo)serverResponseInfo.getData();
        // /*if (StringUtils.isBlank(siteInfo.getSiteHost()) ||
        // StringUtils.isBlank(siteInfo.getSiteEmailTo())) {
        // return ServerResponse.createByErrorMsg("请先设置Host and ToEmail");
        // }*/
        // } else {
        // return serverResponseInfo;
        // }
        // 创建一个时间格式化对象
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        String inputStartTime = "09:00";
        String inputEndTime = "17:00";
        // 获取当前时间
        LocalTime now = LocalTime.now();

//        SiteProduct siteProduct = iSiteProductService.getProductSetting();
//        if (siteProduct.getHolidayDisplay()) {
//            // 周末固定13点到17点
//            inputStartTime = "13:00";
//            inputEndTime = "17:00";
//            LocalTime startTime = LocalTime.parse(inputStartTime, formatter);
//            LocalTime endTime = LocalTime.parse(inputEndTime, formatter);
//            if (now.isBefore(startTime) || now.isAfter(endTime)) {
//                return ServerResponse.createByErrorMsg("当前不在银证转入服务时间内，周末银证转入时间在13:00-17:00之间");
//            }
//            // return ServerResponse.createByErrorMsg("周末或节假日不在银证转入服务时间内！");
//        }

        SiteSetting siteSetting = iSiteSettingService.getSiteSetting();

        if (StrUtil.isNotBlank(siteSetting.getRechargeBegin())) {
            inputStartTime = siteSetting.getRechargeBegin();
        }

        if (StrUtil.isNotBlank(siteSetting.getRechargeEnd())) {
            inputEndTime = siteSetting.getRechargeEnd();
        }
        // 解析输入的时间字符串到LocalTime对象
        LocalTime startTime = LocalTime.parse(inputStartTime, formatter);
        LocalTime endTime = LocalTime.parse(inputEndTime, formatter);
        if (now.isBefore(startTime) || now.isAfter(endTime)) {
            return ServerResponse.createByErrorMsg("当前不在银证转入服务时间内");
        }

        User user = this.iUserService.getCurrentRefreshUser(request);
        if (user.getIsActive().intValue() != 2) {
            return ServerResponse.createByErrorMsg("未实名认证不能发起充值");
        }
        // if (user.getWithPwd() == null || !user.getWithPwd().equals(password)) {
        // return ServerResponse.createByErrorMsg("请先设置资金密码，或密码错误");
        // }

        // 充值次数限制
        ServerResponse serverResponse = checkInMoney(30, user.getId());
        if (!serverResponse.isSuccess()) {
            return serverResponse;
        }

        // // 删除旧的未支付订单
        // List<UserRecharge> userChargeList =
        // userRechargeMapper.findUserChargeList(user.getId(), null, NumberUtils.INTEGER_ZERO);
        // if (CollectionUtil.isNotEmpty(userChargeList)) {
        // userChargeList.forEach(recharge -> {
        // userRechargeMapper.deleteByPrimaryKey(recharge.getId());
        // });
        // }

        UserRecharge userRecharge = new UserRecharge();

        userRecharge.setUserId(user.getId());
        userRecharge.setNickName(user.getRealName());
        userRecharge.setAgentId(user.getAgentId());

        String ordersn = KeyUtils.getRechargeOrderSn();
        userRecharge.setOrderSn(ordersn);

        userRecharge.setPayChannel(payType);
        userRecharge.setPayAmt(new BigDecimal(amt));
        userRecharge.setOrderStatus(Integer.valueOf(0));
        userRecharge.setAddTime(new Date());
        userRecharge.setPayChannel("银证转入");

        this.userRechargeMapper.insert(userRecharge);
        String payUrl = payService.sendThirdPayRequest(userRecharge, request);
        log.info("【sendThirdPayRequest】三方支付返回结果=>订单号：{}，支付地址：{}", userRecharge.getOrderSn(), payUrl);
        if (StrUtil.isNotBlank(payUrl)) {
            return ServerResponse.createBySuccess("订单创建成功", payUrl);
        }

        // String email_token = KeyUtils.getUniqueKey();
        //
        // String redisSetExResult = RedisShardedPoolUtils.setEx(email_token, email_token, 300);
        //
        // log.info("用户充值，保存redis token成功，redisSetExResult = {}", redisSetExResult);
        /*SendHTMLMail.send(user, userRecharge, email_token, siteInfo
                .getSiteHost(), siteInfo.getSiteEmailTo());
        log.info("用户充值，发送审核邮件成功");*/
        // return ServerResponse.createBySuccessMsg("创建支付订单成功！");
        return ServerResponse.createByErrorMsg("订单创建失败，请联系客服");
    }

    @Override
    public ServerResponse inMoneyByAdmin(String amt, String payType, Integer uid, String remark, String phone,
                                         String operator) {
        if (StringUtils.isBlank(amt) || StringUtils.isBlank(payType)) {
            return ServerResponse.createByErrorMsg("参数不能为空");
        }
        User user = (User) iUserService.findByUserId(uid).getData();

        UserRecharge userRecharge = new UserRecharge();

        userRecharge.setUserId(user.getId());
        userRecharge.setNickName(user.getRealName());
        userRecharge.setAgentId(user.getAgentId());

        String ordersn = KeyUtils.getRechargeOrderSn();
        userRecharge.setOrderSn(ordersn);

        userRecharge.setPayChannel(payType);
        userRecharge.setPayAmt(new BigDecimal(amt));
        userRecharge.setOrderStatus(Integer.valueOf(1));
        userRecharge.setAddTime(new Date());
        userRecharge.setPayTime(new Date());
        userRecharge.setRemark(remark);
        userRecharge.setPhone(phone);
        userRecharge.setOperator(operator);

        int insertCount = this.userRechargeMapper.insert(userRecharge);
        if (insertCount > 0) {

            String email_token = KeyUtils.getUniqueKey();

            // String redisSetExResult = RedisShardedPoolUtils.setEx(email_token, email_token, 300);

            // log.info("用户充值，保存redis token成功，redisSetExResult = {}", redisSetExResult);

            /*SendHTMLMail.send(user, userRecharge, email_token, siteInfo
                    .getSiteHost(), siteInfo.getSiteEmailTo());
            log.info("用户充值，发送审核邮件成功");*/
            return ServerResponse.createBySuccessMsg("创建支付订单成功！");
        }
        return ServerResponse.createByErrorMsg("创建支付订单失败");
    }

    public ServerResponse findUserRechargeByOrderSn(String orderSn) {
        UserRecharge userRecharge = this.userRechargeMapper.findUserRechargeByOrderSn(orderSn);
        if (userRecharge != null) {
            return ServerResponse.createBySuccess(userRecharge);
        }
        return ServerResponse.createByErrorMsg("找不到充值订单");
    }

    @Transactional
    public ServerResponse chargeSuccess(UserRecharge userRecharge) throws Exception {
        log.info("充值订单 确认成功操作 id = {}", userRecharge.getId());

        if (userRecharge.getOrderStatus().intValue() != 0) {
            return ServerResponse.createByErrorMsg("订单状态不能重复修改");
        }

        User user = this.userMapper.selectByPrimaryKey(userRecharge.getUserId());
        if (user == null) {
            return ServerResponse.createByErrorMsg("用户不存在");
        }
        BigDecimal userAmt_before = user.getUserAmt();
        BigDecimal enableAmt_before = user.getEnableAmt();
        user.setUserAmt(userAmt_before.add(userRecharge.getPayAmt()));
        user.setEnableAmt(enableAmt_before.add(userRecharge.getPayAmt()));
        int updateCount = this.userMapper.updateByPrimaryKeySelective(user);
        if (updateCount > 0) {
            log.info("1.修改用户资金成功");
        } else {
            return ServerResponse.createByErrorMsg("失败，修改用户资金失败");
        }

        userRecharge.setOrderStatus(Integer.valueOf(1));
        userRecharge.setPayTime(new Date());
        int updateCCount = this.userRechargeMapper.updateByPrimaryKeySelective(userRecharge);
        if (updateCCount > 0) {
            log.info("2.修改订单状态成功");
        } else {
            throw new Exception("2. 修改订单状态失败!");
        }

        UserCashDetail ucd = new UserCashDetail();
        ucd.setAgentId(user.getAgentId());
        ucd.setAgentName(user.getAgentName());
        ucd.setUserId(user.getId());
        ucd.setUserName(user.getRealName());
        ucd.setDeType("用户充值");
        ucd.setDeAmt(userRecharge.getPayAmt());
        ucd.setDeSummary("用户充值成功，充值前总金额:" + userAmt_before + ",充值后总金额:" + user.getUserAmt() + ",充值前可用:"
                + enableAmt_before + ",充值后可用:" + user.getEnableAmt());

        ucd.setAddTime(new Date());
        ucd.setIsRead(Integer.valueOf(0));
        int insertCount = this.userCashDetailMapper.insert(ucd);
        if (insertCount > 0) {
            return ServerResponse.createBySuccessMsg("充值成功！");
        }
        return ServerResponse.createByErrorMsg("充值失败");
    }

    public ServerResponse chargeFail(UserRecharge userRecharge) throws Exception {
        if (userRecharge.getOrderStatus().intValue() != 0) {
            return ServerResponse.createByErrorMsg("订单状态不能重复修改");
        }

        userRecharge.setOrderStatus(Integer.valueOf(2));
        int updateCCount = this.userRechargeMapper.updateByPrimaryKeySelective(userRecharge);
        if (updateCCount > 0) {
            return ServerResponse.createBySuccessMsg("订单已修改为失败");
        }
        return ServerResponse.createByErrorMsg("修改出现异常");
    }

    public ServerResponse chargeCancel(UserRecharge userRecharge) throws Exception {
        if (userRecharge.getOrderStatus().intValue() != 0) {
            return ServerResponse.createByErrorMsg("订单状态不能重复修改");
        }

        userRecharge.setOrderStatus(Integer.valueOf(3));
        int updateCCount = this.userRechargeMapper.updateByPrimaryKeySelective(userRecharge);
        if (updateCCount > 0) {
            return ServerResponse.createBySuccessMsg("订单取消成功");
        }
        return ServerResponse.createByErrorMsg("订单取消出现异常");
    }

    public ServerResponse<PageInfo> findUserChargeList(String payChannel, Integer orderStatus,
                                                       HttpServletRequest request, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);

        User user = this.iUserService.getCurrentUser(request);

        List<UserRecharge> userRecharges =
                this.userRechargeMapper.findUserChargeList(user.getId(), payChannel, orderStatus);

        log.info("充值列表，增加用户 {} ，payChannel = {} , orderStatus = {}， 数量 = {}",
                new Object[]{user.getId(), payChannel, orderStatus, userRecharges.size()});

        PageInfo pageInfo = new PageInfo(userRecharges);

        return ServerResponse.createBySuccess(pageInfo);
    }

    public ServerResponse<PageInfo> listByAgent(Integer agentId, String realName, String payChannel, Integer state,
                                                HttpServletRequest request, int pageNum, int pageSize, String phone) {
        AgentUser currentAgent = this.iAgentUserService.getCurrentAgent(request);

        if (agentId != null) {
            AgentUser agentUser = this.agentUserMapper.selectByPrimaryKey(agentId);
            if (agentUser.getParentId() != currentAgent.getId()) {
                return ServerResponse.createByErrorMsg("不能查询非下级代理记录");
            }
        }
        Integer searchId = null;
        if (agentId == null) {
            searchId = currentAgent.getId();
        } else {
            searchId = agentId;
        }

        PageHelper.startPage(pageNum, pageSize);

        List<UserRecharge> userRecharges =
                this.userRechargeMapper.listByAgent(searchId, realName, payChannel, state, phone);

        PageInfo pageInfo = new PageInfo(userRecharges);

        return ServerResponse.createBySuccess(pageInfo);
    }

    public ServerResponse listByAdmin(Integer agentId, Integer userId, String realName, Integer state, String beginTime,
                                      String endTime, HttpServletRequest request, int pageNum, int pageSize, Integer accountType, String phone,
                                      String orderNo) {
        PageHelper.startPage(pageNum, pageSize);

        Timestamp begin_time = null;
        if (StringUtils.isNotBlank(beginTime)) {
            begin_time = DateTimeUtil.searchStrToTimestamp(beginTime);
        }
        Timestamp end_time = null;
        if (StringUtils.isNotBlank(endTime)) {
            end_time = DateTimeUtil.searchStrToTimestamp(endTime);
        }

        List<UserRecharge> userRecharges = this.userRechargeMapper.listByAdmin(agentId, userId, realName, state,
                begin_time, end_time, accountType, phone, orderNo);
        if (CollectionUtil.isNotEmpty(userRecharges)) {
            List<AgentUser> agentUserList = agentUserService.findAll();
            for (UserRecharge userRecharge : userRecharges) {
                if (Objects.nonNull(userRecharge.getAgentId())) {
                    for (AgentUser agentUser : agentUserList) {
                        if (agentUser.getId().equals(userRecharge.getAgentId())) {
                            userRecharge.setAgentName(agentUser.getAgentName());
                        }
                    }
                } else {
                    userRecharge.setAgentName("无代理");
                }
            }
        }

        PageInfo pageInfo = new PageInfo(userRecharges);

        return ServerResponse.createBySuccess(pageInfo);
    }

    @Transactional
    public ServerResponse updateState(Integer chargeId, Integer state) throws Exception {
        UserRecharge userRecharge = this.userRechargeMapper.selectByPrimaryKey(chargeId);

        if (userRecharge == null) {
            return ServerResponse.createByErrorMsg("充值订单不存在");
        }
        if (userRecharge.getOrderStatus().intValue() != 0) {
            return ServerResponse.createByErrorMsg("订单状态不是下单状态不能更改");
        }

        if (state.intValue() == 1) {

            User user = this.userMapper.selectByPrimaryKey(userRecharge.getUserId());
            if (user == null) {
                return ServerResponse.createByErrorMsg("用户不存在");
            }
            BigDecimal user_amt = user.getUserAmt().add(userRecharge.getPayAmt());
            log.info("管理员确认订单成功，增加用户 {} 总资金，原金额 = {} , 增加后 = {}",
                    new Object[]{user.getId(), user.getUserAmt(), user_amt});
            user.setUserAmt(user_amt);
            BigDecimal user_enable_amt = user.getEnableAmt().add(userRecharge.getPayAmt());
            log.info("管理员确认订单成功，增加用户 {} 可用资金，原金额 = {} , 增加后 = {}",
                    new Object[]{user.getId(), user.getEnableAmt(), user_enable_amt});
            user.setEnableAmt(user_enable_amt);

            int updateCount = this.userMapper.updateByPrimaryKeySelective(user);
            if (updateCount > 0) {
                log.info("修改用户资金成功！");
            } else {
                log.error("修改用户资金出错，抛出异常");
                throw new Exception("修改用户资金出错，抛出异常");
            }
        }

        userRecharge.setOrderStatus(Integer.valueOf((state.intValue() == 1) ? 1 : 2));

        userRecharge.setPayTime(new Date());
        int updateCount = this.userRechargeMapper.updateByPrimaryKeySelective(userRecharge);
        if (updateCount > 0) {
            return ServerResponse.createBySuccessMsg("修改订单状态成功！");
        }
        return ServerResponse.createByErrorMsg("修改订单状态失败！");
    }

    public ServerResponse createOrder(Integer userId, Integer state, Integer amt, String payChannel) {
        if (userId == null || state == null || amt == null) {
            return ServerResponse.createByErrorMsg("参数不能为空");
        }

        User user = this.userMapper.selectByPrimaryKey(userId);
        if (user == null) {
            return ServerResponse.createByErrorMsg("找不到用户");
        }

        UserRecharge userRecharge = new UserRecharge();
        userRecharge.setUserId(user.getId());
        userRecharge.setNickName(user.getRealName());
        userRecharge.setAgentId(user.getAgentId());

        String ordersn = KeyUtils.getRechargeOrderSn();
        userRecharge.setOrderSn(ordersn);

        userRecharge.setPayChannel(payChannel);
        userRecharge.setPayAmt(new BigDecimal(amt.intValue()));
        userRecharge.setAddTime(new Date());
        userRecharge.setPayTime(new Date());

        if (state.intValue() == 0) {
            userRecharge.setOrderStatus(Integer.valueOf(0));
        } else if (state.intValue() == 1) {
            userRecharge.setOrderSn(payChannel);
            userRecharge.setPayChannel("2");
            userRecharge.setOrderStatus(Integer.valueOf(1));

            user.setUserAmt(user.getUserAmt().add(new BigDecimal(amt.intValue())));
            user.setEnableAmt(user.getEnableAmt().add(new BigDecimal(amt.intValue())));
            this.userMapper.updateByPrimaryKeySelective(user);
        } else if (state.intValue() == 2) {
            userRecharge.setOrderStatus(Integer.valueOf(2));
        } else {
            return ServerResponse.createByErrorMsg("订单状态不正确");
        }

        int insertCount = this.userRechargeMapper.insert(userRecharge);
        if (insertCount > 0) {
            return ServerResponse.createBySuccessMsg("生成订单成功！");
        }
        return ServerResponse.createByErrorMsg("生成订单失败，请重试");
    }

    public ServerResponse del(Integer cId) {
        if (cId == null) {
            return ServerResponse.createByErrorMsg("id不能为空");
        }
        int updateCount = this.userRechargeMapper.deleteByPrimaryKey(cId);
        if (updateCount > 0) {
            return ServerResponse.createBySuccessMsg("删除成功");
        }
        return ServerResponse.createByErrorMsg("删除失败");
    }

    public int deleteByUserId(Integer userId) {
        return this.userRechargeMapper.deleteByUserId(userId);
    }

    public BigDecimal CountChargeSumAmt(Integer accountType, Integer agentId) {
        return this.userRechargeMapper.CountChargeSumAmt(accountType, agentId);
    }

    public BigDecimal CountTotalRechargeAmountByTime(Integer accountType, Integer agentId) {
        return this.userRechargeMapper.CountTotalRechargeAmountByTime(accountType, agentId);
    }

    @Override
    public List<UserRecharge> exportByAdmin(Integer agentId, Integer userId, String realName, Integer state,
                                            String beginTime, String endTime, HttpServletRequest request, Integer accountType) {
        Timestamp begin_time = null;
        if (StringUtils.isNotBlank(beginTime)) {
            begin_time = DateTimeUtil.searchStrToTimestamp(beginTime);
        }
        Timestamp end_time = null;
        if (StringUtils.isNotBlank(endTime)) {
            end_time = DateTimeUtil.searchStrToTimestamp(endTime);
        }

        List<UserRecharge> userRecharges = this.userRechargeMapper.listByAdmin(agentId, userId, realName, state,
                begin_time, end_time, accountType, null, null);

        return userRecharges;
    }

    @Override
    public BigDecimal getUserTotalRechargeAmountByStatus(Integer userId, Integer status) {
        return Optional.ofNullable(userRechargeMapper.getUserTotalRechargeAmountByStatus(userId, status))
                .orElse(BigDecimal.ZERO);
    }

    @Override
    public List<UserRecharge> getTodayFirstRecharge() {
        return userRechargeMapper.getTodayFirstRecharge();
    }

    private String sendThirdPayRequest(UserRecharge userRecharge, HttpServletRequest request) {
        // userName test String 是 是 商户登录名
        // amount 10.00 String 是 是 订单金额 单位：元
        // 保留小数点后两位
        // payType quickpay String 否 是 充值类型，特定通道需要传，通常不用传递
        // outOrderId T1234567 String 是 是 商户订单号 需保持唯一
        // returnUrl http://www.xxxxxx.com/Notify_Url.jsp String 是 是 后端异步回调地址，用于通知支付结果
        // frontReturnUrl http://www.xxxxxx.com/index.jsp String 是 是 前端支付完成跳转地址，用于在支付结束后回到业务地址

        Map<String, Object> params = new LinkedHashMap<>();
        // BigInteger payAmount = userRecharge.getPayAmt().multiply(new BigDecimal(100)).toBigInteger();
        String domain = request.getHeader("host");
        if (StrUtil.isEmpty(domain)) {
            domain = request.getServerName();
        }
        String fullDomain = request.getScheme() + "://" + domain + "/api/pay/callback.do";

        params.put("merchantId", "147");
        params.put("productId", "8007");
        params.put("orderNo", userRecharge.getOrderSn()); // 商户订单号
        params.put("currency", "cny");
        params.put("amount", userRecharge.getPayAmt()); // 支付结果后台回调URL
        params.put("clientIP", IpUtils.getIp(request));
//        params.put("clientIP","************");
        params.put("callbackUrl", fullDomain);
        params.put("commodity", "commodity");
        params.put("productDesc", "productDesc");

        String signStr = MapUtil.mapToQueryString(params);
        signStr += "&key=3M7OZaz7Cw5SmKda9ALSyJae8LP61eXt6iIcG57W0JuOttNRGyuuVtU7QybTPUBE22bV2YOcttWhrDuJR9tXs1Ykhe23IaGI3qiasGGGBuOSfFWJWOIQiU7ZwM6VxgrX";
        log.info("【sendThirdPayRequest】发起三方支付=>订单号：{}，加密前字符串拼接：{}", userRecharge.getOrderSn(), signStr);
        String sign = SecureUtil.md5(signStr).toUpperCase();
        params.put("sign", sign);

        log.info("【sendThirdPayRequest】发起三方支付=>订单号：{}，请求参数：{}", userRecharge.getOrderSn(), JSONUtil.toJsonStr(params));
        String response =
                HttpUtil.post("http://************:8096/sfang-api/pay/createOrder", JSONUtil.toJsonStr(params));
        log.info("【sendThirdPayRequest】三方支付返回结果=>订单号：{}，返回结果：{}", userRecharge.getOrderSn(), response);
        JSONObject jsonObject = JSON.parseObject(response);
        if ("0".equals(jsonObject.getString("code"))) {
            JSONObject data = jsonObject.getJSONObject("data");
            return data.getString("payParams");
        }
        return null;
    }

    @Override
    public void payCallback(Map<String, Object> callbackRequestParamMap, HttpServletRequest request,
                            HttpServletResponse response) throws Exception {

        String mchOrderNo = String.valueOf(callbackRequestParamMap.get("payNo"));
        // 无三方订单号！！
        String payOrderId = String.valueOf(callbackRequestParamMap.get("payNo"));
        String status = String.valueOf(callbackRequestParamMap.get("status"));
        // 请求⽀付下单时⾦额,保留两位小数
        String amount = String.valueOf(callbackRequestParamMap.get("amount"));
        log.info(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，支付状态：{}，下单金额:{},实际支付金额：{}", mchOrderNo, payOrderId, status,
                amount, amount);
        UserRecharge userRecharge = this.userRechargeMapper.findUserRechargeByOrderSn(mchOrderNo);
        if (userRecharge == null) {
            log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，系统找不到对应的订单", mchOrderNo, payOrderId);
            response.getWriter().write("success");
            return;
        }
        if (Objects.equals(userRecharge.getOrderStatus(), NumberUtils.INTEGER_ONE)) {
            log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，已处理过，不再处理", mchOrderNo, payOrderId);
            response.getWriter().write("success");
            return;
        }

        User user = this.userMapper.selectByPrimaryKey(userRecharge.getUserId());
        if (user == null) {
            log.error(">>支付回调-payCallback，系统订单号：{}，三方单号：{}，系统找不到对应的用户", mchOrderNo, payOrderId);
            response.getWriter().write("success");
            return;
        }

        // if (!amount.equals(income)) {
        // log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，下单金额与实际付款金额不匹配，取实际付款金额", mchOrderNo, payOrderId);
        // userRecharge.setPayAmt(new BigDecimal(income));
        // }
        userRecharge.setPayTime(new Date());

        if (status.equals("S")) {
            userRecharge.setOrderStatus(NumberUtils.INTEGER_ONE);
            log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，订单状态【支付成功】", mchOrderNo, payOrderId);
        } else {
            userRecharge.setOrderStatus(NumberUtils.INTEGER_TWO);
            log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，订单状态【失败】", mchOrderNo, payOrderId);
        }

//        switch (status) {
//            case "0":
//                userRecharge.setOrderStatus(NumberUtils.INTEGER_TWO);
//                log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，订单状态【失败】", mchOrderNo, payOrderId);
//                break;
//            case "1":
//                userRecharge.setOrderStatus(NumberUtils.INTEGER_ONE);
//                log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，订单状态【支付成功】", mchOrderNo, payOrderId);
//                response.getWriter().write("success");
//                break;
//            default:
//                userRecharge.setOrderStatus(NumberUtils.INTEGER_TWO);
//                log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，订单状态【失败】", mchOrderNo, payOrderId);
//                break;
//        }
        // ⽀付中⼼⽣成的订单号
        userRecharge.setPaySn(payOrderId);
        // 取支付的金额
        userRecharge.setPayAmt(new BigDecimal(amount));

        log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，开始修改订单状态", mchOrderNo, payOrderId);
        int updateCount = this.userRechargeMapper.updateByPrimaryKeySelective(userRecharge);
        if (NumberUtils.INTEGER_ONE.equals(userRecharge.getOrderStatus())) {
            BigDecimal totalAmt = user.getUserAmt().add(userRecharge.getPayAmt());
            user.setUserAmt(totalAmt);
            BigDecimal totalEnable = user.getEnableAmt().add(userRecharge.getPayAmt());
            user.setEnableAmt(totalEnable);
            int updateUserCount = this.userMapper.updateByPrimaryKeySelective(user);
            if (updateUserCount > 0) {
                log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，支付回调业务处理【成功】！！", mchOrderNo, payOrderId);
            }
        }
        response.getWriter().write("success");
        log.info(">>支付回调-payCallback,系统订单号：{}，三方单号：{}，支付回调业务处理流程【结束】！！", mchOrderNo, payOrderId);
    }
}
