package com.nq.service.impl;

import com.nq.common.ServerResponse;
import com.nq.dao.AgentUserMapper;
import com.nq.pojo.AgentUser;
import com.nq.service.AgentUserBizService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 代理用户业务服务实现类
 * <AUTHOR>
 * @since 2025/6/3 17:30
 */
@Service("agentUserBizService")
public class AgentUserBizServiceImpl implements AgentUserBizService {

    private static final Logger log = LoggerFactory.getLogger(AgentUserBizServiceImpl.class);

    @Autowired
    private AgentUserMapper agentUserMapper;

    @Override
    public ServerResponse<List<AgentUser>> getSubordinateAgents(Integer agentId) {
        if (agentId == null) {
            return ServerResponse.createByErrorMsg("代理ID不能为空");
        }

        try {
            // 验证代理是否存在
            AgentUser currentAgent = agentUserMapper.selectByPrimaryKey(agentId);
            if (currentAgent == null) {
                return ServerResponse.createByErrorMsg("代理不存在");
            }

            // 获取所有代理用户
            List<AgentUser> allAgents = agentUserMapper.findAll();
            if (allAgents == null || allAgents.isEmpty()) {
                return ServerResponse.createBySuccess("查询成功", new ArrayList<>());
            }

            // 递归获取所有下级代理
            List<AgentUser> subordinateAgents = getAllSubordinateAgents(agentId, allAgents);

            log.info("代理ID: {} 的下级代理数量: {}", agentId, subordinateAgents.size());
            return ServerResponse.createBySuccess("查询成功", subordinateAgents);

        } catch (Exception e) {
            log.error("获取下级代理失败", e);
            return ServerResponse.createByErrorMsg("查询失败：" + e.getMessage());
        }
    }

    @Override
    public ServerResponse<List<AgentUser>> getAgentTreeStructure() {
        try {
            // 获取所有代理用户
            List<AgentUser> allAgents = agentUserMapper.findAll();
            if (allAgents == null || allAgents.isEmpty()) {
                return ServerResponse.createBySuccess("查询成功", new ArrayList<>());
            }

            // 构建树形结构
            List<AgentUser> treeStructure = buildAgentTree(allAgents);

            log.info("构建代理树形结构成功，根节点数量: {}", treeStructure.size());
            return ServerResponse.createBySuccess("查询成功", treeStructure);

        } catch (Exception e) {
            log.error("构建代理树形结构失败", e);
            return ServerResponse.createByErrorMsg("查询失败：" + e.getMessage());
        }
    }

    /**
     * 递归获取所有下级代理
     * @param parentId 父代理ID
     * @param allAgents 所有代理列表
     * @return 下级代理列表
     */
    private List<AgentUser> getAllSubordinateAgents(Integer parentId, List<AgentUser> allAgents) {
        List<AgentUser> subordinates = new ArrayList<>();

        // 查找直接下级
        List<AgentUser> directChildren = allAgents.stream()
                .filter(agent -> agent.getParentId() != null && agent.getParentId().equals(parentId))
                .collect(Collectors.toList());

        // 添加直接下级
        subordinates.addAll(directChildren);

        // 递归查找间接下级
        for (AgentUser child : directChildren) {
            List<AgentUser> grandChildren = getAllSubordinateAgents(child.getId(), allAgents);
            subordinates.addAll(grandChildren);
        }

        return subordinates;
    }

    /**
     * 构建代理树形结构
     * @param allAgents 所有代理列表
     * @return 树形结构的代理列表
     */
    private List<AgentUser> buildAgentTree(List<AgentUser> allAgents) {
        // 查找根节点（parentId 为 0 或 null 的节点）
        List<AgentUser> rootNodes = allAgents.stream()
                .filter(agent -> agent.getParentId() == null || agent.getParentId().equals(0))
                .collect(Collectors.toList());

        // 为每个根节点设置子节点
        for (AgentUser rootNode : rootNodes) {
            setChildren(rootNode, allAgents);
        }

        return rootNodes;
    }

    /**
     * 递归设置子节点
     * @param parent 父节点
     * @param allAgents 所有代理列表
     */
    private void setChildren(AgentUser parent, List<AgentUser> allAgents) {
        // 查找当前节点的直接子节点
        List<AgentUser> children = allAgents.stream()
                .filter(agent -> agent.getParentId() != null && agent.getParentId().equals(parent.getId()))
                .collect(Collectors.toList());

        if (!children.isEmpty()) {
            // 设置子节点
            parent.setChildren(children);

            // 递归为每个子节点设置子节点
            for (AgentUser child : children) {
                setChildren(child, allAgents);
            }
        } else {
            // 如果没有子节点，设置为空列表
            parent.setChildren(new ArrayList<>());
        }
    }
}
