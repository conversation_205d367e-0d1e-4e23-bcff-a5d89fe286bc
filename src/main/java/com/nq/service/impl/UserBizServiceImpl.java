package com.nq.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dao.UserMapper;
import com.nq.dto.UserPositionDto;
import com.nq.pojo.AgentUser;
import com.nq.pojo.User;
import com.nq.pojo.UserSignature;
import com.nq.service.*;
import com.nq.utils.UserInfoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户业务服务实现类
 * <AUTHOR>
 * @since 2025/1/15
 */
@Service("userBizService")
public class UserBizServiceImpl implements UserBizService {
    
    private static final Logger log = LoggerFactory.getLogger(UserBizServiceImpl.class);
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private AgentUserBizService agentUserBizService;
    
    @Autowired
    private IUserSignatureService iUserSignatureService;
    
    @Autowired
    private IUserPositionService iUserPositionService;
    
    @Autowired
    private IUserRechargeService iUserRechargeService;
    
    @Autowired
    private IUserWithdrawService iUserWithdrawService;
    
    @Autowired
    private IUserStockSubscribeBizService userStockSubscribeBizService;
    
    @Override
    public ServerResponse<PageInfo> listByAdminWithSubAgents(String realName, String phone, Integer agentId, 
                                                            Integer accountType, int pageNum, int pageSize, 
                                                            Integer status, HttpServletRequest request) {
        try {
            // 获取当前代理用户
            if (Objects.isNull(agentId)) {
                AgentUser currentAgentUser = UserInfoUtil.getCurrentAgentUser(request);
                if (Objects.nonNull(currentAgentUser)) {
                    agentId = currentAgentUser.getId();
                }
            }
            
            if (agentId == null) {
                log.warn("无法获取当前代理用户信息");
                return ServerResponse.createByErrorMsg("无法获取当前代理用户信息");
            }
            
            // 获取当前代理下的所有下级代理ID列表
            List<Integer> agentIds = getSubordinateAgentIds(agentId);
            
            // 开始分页
            PageHelper.startPage(pageNum, pageSize);
            
            // 查询用户列表（包含当前代理和所有下级代理的用户）
            List<User> users = userMapper.listByAdminWithAgentIds(realName, phone, agentIds, accountType, status);
            
            // 清除分页参数
            PageHelper.clearPage();
            
            // 处理用户签名信息
            processUserSignatures(users);
            
            // 处理用户持仓和资金信息
            processUserFinancialInfo(users);
            
            // 创建分页信息
            PageInfo<User> pageInfo = new PageInfo<>(users);
            
            log.info("查询代理ID: {} 下所有代理的用户，共查询到 {} 个用户", agentId, users.size());
            return ServerResponse.createBySuccess(pageInfo);
            
        } catch (Exception e) {
            log.error("查询代理下所有用户失败", e);
            return ServerResponse.createByErrorMsg("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取当前代理下的所有下级代理ID列表（包含自己）
     * @param agentId 当前代理ID
     * @return 代理ID列表
     */
    private List<Integer> getSubordinateAgentIds(Integer agentId) {
        try {
            // 获取所有下级代理
            ServerResponse<List<AgentUser>> subordinateResponse = agentUserBizService.getSubordinateAgents(agentId);
            
            List<Integer> agentIds = subordinateResponse.getData().stream()
                    .map(AgentUser::getId)
                    .collect(Collectors.toList());
            
            // 添加当前代理ID
            agentIds.add(agentId);
            
            log.info("代理ID: {} 包含的所有代理ID: {}", agentId, agentIds);
            return agentIds;
            
        } catch (Exception e) {
            log.error("获取下级代理ID列表失败", e);
            // 如果获取下级代理失败，至少返回当前代理ID
            return List.of(agentId);
        }
    }
    
    /**
     * 处理用户签名信息
     * @param users 用户列表
     */
    private void processUserSignatures(List<User> users) {
        if (CollectionUtil.isNotEmpty(users)) {
            List<Integer> userIdList = users.stream().map(User::getId).collect(Collectors.toList());
            List<UserSignature> userSignatureList = iUserSignatureService.findByUserIdList(userIdList);
            
            if (CollectionUtil.isNotEmpty(userSignatureList)) {
                Map<Integer, String> userSignatureMap = userSignatureList.stream()
                        .collect(Collectors.toMap(
                                UserSignature::getUserId, 
                                UserSignature::getSignatureMsg, 
                                (oldKey, newKey) -> newKey
                        ));
                
                users.forEach(user -> {
                    user.setSignatureMsg(userSignatureMap.get(user.getId()));
                });
            }
        }
    }
    
    /**
     * 处理用户持仓和资金信息
     * @param users 用户列表
     */
    private void processUserFinancialInfo(List<User> users) {
        if (CollectionUtil.isNotEmpty(users)) {
            for (User user : users) {
                try {
                    // 获取用户持仓信息
                    UserPositionDto userPositionDto = iUserPositionService.getUserPositionDto(user.getId());
                    
                    // 获取充值总额
                    BigDecimal rechargeSumAmt = iUserRechargeService.sumUserRechargeAmt(user.getId());
                    if (rechargeSumAmt == null) {
                        rechargeSumAmt = BigDecimal.ZERO;
                    }
                    
                    // 获取提现总额
                    BigDecimal withdrawSumAmt = iUserWithdrawService.sumUserWithdrawAmt(user.getId());
                    if (withdrawSumAmt == null) {
                        withdrawSumAmt = BigDecimal.ZERO;
                    }
                    
                    // 计算目前仓位 = 只包含持仓中的盈亏+平仓的盈利+充值-提现
                    BigDecimal currentPositionAmt = rechargeSumAmt
                            .subtract(withdrawSumAmt)
                            .add(userPositionDto.getSumAllProfitAndLossAmt());
                    
                    // 获取新股冻结的盈利
                    BigDecimal newStockProfitAmt = userStockSubscribeBizService.sumUserStockSubscribeProfit(user.getId());
                    if (newStockProfitAmt == null) {
                        newStockProfitAmt = BigDecimal.ZERO;
                    }
                    
                    // 计算总持仓 = 持仓中的盈亏+平仓的盈利+充值 + 加新股冻结的盈利-提现
                    BigDecimal totalPositionAmt = currentPositionAmt.add(newStockProfitAmt);
                    
                    // 设置用户财务信息
                    user.setTotalBuyPrice(userPositionDto.getSumPositionBuyAmt());
                    user.setCurrentPositionAmt(currentPositionAmt);
                    user.setTotalPositionAmt(totalPositionAmt);
                    
                } catch (Exception e) {
                    log.error("处理用户ID: {} 的财务信息失败", user.getId(), e);
                    // 设置默认值
                    user.setTotalBuyPrice(BigDecimal.ZERO);
                    user.setCurrentPositionAmt(BigDecimal.ZERO);
                    user.setTotalPositionAmt(BigDecimal.ZERO);
                }
            }
        }
    }
}
