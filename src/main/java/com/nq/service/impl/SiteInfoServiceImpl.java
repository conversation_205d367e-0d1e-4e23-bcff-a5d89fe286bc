package com.nq.service.impl;

import cn.hutool.core.util.StrUtil;
import com.nq.common.ServerResponse;
import com.nq.dao.AgentUserMapper;
import com.nq.dao.SiteInfoMapper;
import com.nq.pojo.AgentUser;
import com.nq.pojo.SiteInfo;
import com.nq.pojo.User;
import com.nq.service.ISiteInfoService;
import com.nq.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Service("iSiteInfoService")
@Slf4j
public class SiteInfoServiceImpl implements ISiteInfoService {
    @Autowired
    SiteInfoMapper siteInfoMapper;
    @Autowired
    AgentUserMapper agentUserMapper;
    @Autowired
    IUserService iUserService;

    public ServerResponse get() {

        List<SiteInfo> siteInfos = this.siteInfoMapper.findAll();
        if (siteInfos.size() > 0) {
            SiteInfo siteInfo = (SiteInfo) siteInfos.get(0);
            return ServerResponse.createBySuccess(siteInfo);
        }
        return ServerResponse.createByErrorMsg("设置信息不存在");

    }

    public ServerResponse add(SiteInfo siteInfo) {

        List<SiteInfo> siteInfos = this.siteInfoMapper.findAll();

        if (siteInfos.size() > 0) {
            return ServerResponse.createByErrorMsg("不能重复添加");
        }

        if (StringUtils.isBlank(siteInfo.getSiteName()) || StringUtils.isBlank(siteInfo.getSiteLogo())
                || StringUtils.isBlank(siteInfo.getSiteLogoSm())) {
            return ServerResponse.createByErrorMsg("名字和logo不能为空");
        }

        int insertCount = this.siteInfoMapper.insert(siteInfo);
        if (insertCount > 0) {
            return ServerResponse.createBySuccessMsg("添加成功");
        }
        return ServerResponse.createByErrorMsg("添加失败");

    }

    public ServerResponse update(SiteInfo siteInfo) {

        if (siteInfo.getId() == null) {
            return ServerResponse.createByErrorMsg("ID不能为空");
        }

        int updateCount = this.siteInfoMapper.updateByPrimaryKeySelective(siteInfo);

        if (updateCount > 0) {
            return ServerResponse.createBySuccessMsg("修改成功");
        }
        return ServerResponse.createByErrorMsg("修改失败");
    }

    public ServerResponse getInfo(HttpServletRequest request) {

        List<SiteInfo> siteInfos = this.siteInfoMapper.findAll();

        if (siteInfos.size() > 0) {
            log.info("request_domain_1={}", request.getServerName());
            log.info("request_domain_2={}", request.getHeader("host"));
            String domain = request.getHeader("host");
            if (StrUtil.isEmpty(domain)) {
                domain = request.getServerName();
            }
            SiteInfo siteInfo = (SiteInfo) siteInfos.get(0);
            String serviceLine = "chat";
            User currentUser = iUserService.getCurrentUser(request);
            if (currentUser != null) {
                AgentUser agentUser = agentUserMapper.findAgentByAgentId(currentUser.getAgentId());
                if (agentUser != null) {
                    if (StringUtils.isEmpty(agentUser.getServiceLine())) {
                        if (agentUser.getParentId() != 0) {
                            AgentUser parentAgentuser = this.findParentWithServiceLine(agentUser);
                            if (parentAgentuser != null && StringUtils.isNotEmpty(parentAgentuser.getServiceLine())) {
                                serviceLine = parentAgentuser.getServiceLine();
                            }
                        }
                    } else {
                        serviceLine = agentUser.getServiceLine();
                    }

                }
            }

            siteInfo.setOnlineService("https://" + domain + serviceLine);
            /*if (!StringUtils.isEmpty(siteInfo.getTradeAgree())) {
                CmcPayOuterRequestUtil cmcPayOuterRequestUtil = new CmcPayOuterRequestUtil();
                String result = cmcPayOuterRequestUtil.sendGet(siteInfo.getTradeAgree());
                siteInfo.setTradeAgreeText(result);
            }*/
            return ServerResponse.createBySuccess(siteInfo);
        }
        return ServerResponse.createByErrorMsg("设置信息info不存在");

    }

    // 递归查询代理用户，直到父级中有配置serviceLine
    private AgentUser findParentWithServiceLine(AgentUser agentUser) {
        if (agentUser == null) {
            return null;
        }
        if (StringUtils.isNotEmpty(agentUser.getServiceLine())) {
            return agentUser;
        }
        return findParentWithServiceLine(agentUserMapper.selectByPrimaryKey(agentUser.getParentId()));
    }

}
