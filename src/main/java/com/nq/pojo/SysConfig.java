package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/4/4 12:59
 */
@Data
@TableName("sys_config")
public class SysConfig implements Serializable {
    @TableId(type = IdType.AUTO, value = "id")
    private Integer id;
    /**
     * 七牛域名
     */
    private String qiniuDomain;

    /**
     * 支付开关 0-关闭 1-开启
     */
    private Integer payEnabled;

    /**
     * 支付关闭时的提示信息
     */
    private String payDisabledMessage;

    // 充值次数限制相关字段
    /**
     * 充值次数限制开关 0-关闭 1-开启
     */
    private Integer rechargeLimitEnabled;
    /**
     * 每日充值订单最大笔数(不论状态)
     */
    private Integer rechargeLimitDaily;
    /**
     * 充值次数超限时的提示信息
     */
    private String rechargeLimitMessage;
}
