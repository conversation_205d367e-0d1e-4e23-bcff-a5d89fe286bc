package com.nq.pojo;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class UserRecharge {

    private Integer id;
    @Excel(name = "用户id")
    private Integer userId;
    @Excel(name = "用户名")
    private String nickName;
    @Excel(name = "代理id")
    private Integer agentId;
    @Excel(name = "代理名称")
    @TableField(exist = false)
    private String agentName;
    @Excel(name = "订单号")
    private String orderSn;

    private String paySn;
    @Excel(name = "充值渠道", replace = {"支付宝_0", "对公打款_1"})
    private String payChannel;
    @Excel(name = "充值金额")
    private BigDecimal payAmt;
    @Excel(name = "状态", replace = {"审核中_0", "成功_1", "失败_2"})
    private Integer orderStatus;

    private String orderDesc;

    @Excel(name = "申请时间", databaseFormat = "yyyyMMddHHmmss", format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date addTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "支付时间", databaseFormat = "yyyyMMddHHmmss", format = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;
    @Excel(name = "备注")
    private String remark;

    /*支付通道主键id*/
    private Integer payId;

    private String phone;
    private String operator;

    public UserRecharge(Integer id, Integer userId, String nickName, Integer agentId, String orderSn, String paySn,
        String payChannel, BigDecimal payAmt, Integer orderStatus, String orderDesc, Date addTime, Date payTime,
        Integer payId, String phone, String operator) {

        this.id = id;

        this.userId = userId;

        this.nickName = nickName;

        this.agentId = agentId;

        this.orderSn = orderSn;

        this.paySn = paySn;

        this.payChannel = payChannel;

        this.payAmt = payAmt;

        this.orderStatus = orderStatus;

        this.orderDesc = orderDesc;

        this.addTime = addTime;

        this.payTime = payTime;

        this.payId = payId;
        this.phone = phone;
        this.operator = operator;

    }

    public UserRecharge() {}

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Integer getAgentId() {
        return agentId;
    }

    public void setAgentId(Integer agentId) {
        this.agentId = agentId;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public String getPaySn() {
        return paySn;
    }

    public void setPaySn(String paySn) {
        this.paySn = paySn;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public BigDecimal getPayAmt() {
        return payAmt;
    }

    public void setPayAmt(BigDecimal payAmt) {
        this.payAmt = payAmt;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getOrderDesc() {
        return orderDesc;
    }

    public void setOrderDesc(String orderDesc) {
        this.orderDesc = orderDesc;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Integer getPayId() {
        return payId;
    }

    public void setPayId(Integer payId) {
        this.payId = payId;
    }
}