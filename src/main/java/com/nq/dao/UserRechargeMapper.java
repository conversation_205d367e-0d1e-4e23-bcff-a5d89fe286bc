package com.nq.dao;

import com.nq.pojo.UserRecharge;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface UserRechargeMapper {
    int deleteByPrimaryKey(Integer paramInteger);

    int insert(UserRecharge paramUserRecharge);

    int insertSelective(UserRecharge paramUserRecharge);

    UserRecharge selectByPrimaryKey(Integer paramInteger);

    int updateByPrimaryKeySelective(UserRecharge paramUserRecharge);

    int updateByPrimaryKey(UserRecharge paramUserRecharge);

    int checkInMoney(@Param("status") int paramInt, @Param("userId") Integer paramInteger);

    UserRecharge findUserRechargeByOrderSn(String paramString);

    List findUserChargeList(@Param("uid") Integer paramInteger, @Param("payChannel") String paramString1,
                            @Param("orderStatus") Integer orderStatus);

    List listByAdmin(@Param("agentId") Integer paramInteger1, @Param("userId") Integer paramInteger2,
                     @Param("realName") String paramString, @Param("state") Integer paramInteger3,
                     @Param("begin_time") Date paramDate1, @Param("end_time") Date paramDate2,
                     @Param("accountType") Integer accountType, @Param("phone") String phone, @Param("orderNo") String orderNo);

    int deleteByUserId(@Param("userId") Integer paramInteger);

    List listByAgent(@Param("searchId") Integer paramInteger1, @Param("realName") String paramString1,
                     @Param("payChannel") String paramString2, @Param("state") Integer paramInteger2, @Param("phone") String phone);

    BigDecimal CountChargeSumAmt(@Param("accountType") Integer accountType, @Param("agentId") Integer agentId);

    BigDecimal CountTotalRechargeAmountByTime(@Param("accountType") Integer accountType,
                                              @Param("agentId") Integer agentId);

    BigDecimal getUserTotalRechargeAmountByStatus(@Param("userId") Integer userId, @Param("status") Integer status);

    List<UserRecharge> getTodayFirstRecharge();

    // 添加统计用户当日充值次数的方法
    int countUserDailyRecharges(Integer userId);
}
