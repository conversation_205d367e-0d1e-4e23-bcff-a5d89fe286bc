package com.nq;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@MapperScan(basePackages = "com.nq.dao")
@EnableScheduling
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@EnableTransactionManagement
public class StockApplication {

    public static void main(String[] args) {
        // System.setProperty("socksProxyHost", "*************");
        // System.setProperty("socksProxyPort", "1080");

        SpringApplication.run(StockApplication.class, args);
    }

}
