package com.nq.utils;

import com.qiniu.http.Response;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import com.qiniu.util.Auth;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class QiniuUtil {

    // 覆盖上传
    public static String getToken(String key) {
        Auth auth = Auth.create("SiGtudrxl76mKIVeQqShWGXt77_YR_SzuUfgbVBK", "o7MNNM-s_ziNoOMjjicd_Q4UeAmha0VOF-QuhchW");
        return auth.uploadToken("caida123", key);
    }

    // 简单上传，默认策略
    public static String getTokenUseForNews() {
        Auth auth = Auth.create("3gizPogd6d4tv0lwg1gJ7-iThLrSWJLF2NPGlViH", "mymoOU5NRTyZci1S_ywxpHBRNmtP57pOZi7q9yqJ");
        return auth.uploadToken("gupiao-ftp");
    }

    /**
     * 上传文件名可空，以文件内容的hash值作为文件名
     */
    public static String uploadFile(String localFilePath, String fileName) {
        try {
            // 构造一个带指定 Region 对象的配置类
            Configuration cfg = new Configuration(Region.region2());
            cfg.resumableUploadAPIVersion = Configuration.ResumableUploadAPIVersion.V2;// 指定分片上传版本
            // 创建上传对象
            UploadManager uploadManager = new UploadManager(cfg);
            // （3）上传文件
            Response response = uploadManager.put(localFilePath, fileName, getTokenUseForNews());
            System.out.println(response.bodyString());
            // （4）解析上传成功结果
            DefaultPutRet putRet = JSONUtil.toBean(response.bodyString(), DefaultPutRet.class);
            return "https://ftpgz.zxhqdj.com/" + putRet.key;
        } catch (Exception e) {
            log.error("qiniu 上传异常:s", e);
        }
        return null;
    }

    public static void main(String[] args) {
        uploadFile("D:\\Snipaste_2024-12-21_12-17-41.png", null);
    }

}
