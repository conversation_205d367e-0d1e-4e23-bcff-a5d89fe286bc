package com.nq.utils;

import java.net.URLEncoder;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/4/7 21:43
 */
public class MapUtil {

    public static String mapToQueryStringWithEncoding(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            try {
                // 对 key 和 value 进行 URL 编码
                String encodedKey = URLEncoder.encode(entry.getKey(), "UTF-8");
                String encodedValue =
                        URLEncoder.encode(entry.getValue() == null ? "" : entry.getValue().toString(), "UTF-8");

                // 拼接 key=value
                result.append(encodedKey).append("=").append(encodedValue).append("&");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 删除最后一个多余的 "&"
        if (result.length() > 0) {
            result.deleteCharAt(result.length() - 1);
        }

        return result.toString();
    }

    public static String mapToQueryString(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            try {
                // 拼接 key=value
                result.append(entry.getKey()).append("=").append(entry.getValue().toString()).append("&");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 删除最后一个多余的 "&"
        if (result.length() > 0) {
            result.deleteCharAt(result.length() - 1);
        }

        return result.toString();
    }

}
