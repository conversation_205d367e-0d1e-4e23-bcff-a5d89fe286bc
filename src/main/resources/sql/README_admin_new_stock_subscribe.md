# 后台管理员帮用户申购新股功能说明

## 概述
实现了后台管理员可以代替用户进行新股申购的功能，管理员可以通过后台接口为指定用户申购新股。

## 功能特性

### 1. 接口信息
- **接口路径**：`/admin/subscribe/addForUser.do`
- **请求方式**：`POST`
- **请求类型**：`application/x-www-form-urlencoded`

### 2. 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userPhone | String | 是 | 用户手机号 |
| newCode | String | 是 | 新股代码 |
| applyNums | Integer | 是 | 申购数量 |
| type | Integer | 是 | 申购类型：1-普通申购，2-需要扣款申购 |

### 3. 申购类型说明
- **type = 1**：普通申购，不扣除用户余额，仅创建申购记录
- **type = 2**：扣款申购，会从用户可用余额中扣除相应的保证金

### 4. 业务逻辑

#### 参数校验
- 验证用户手机号、新股代码、申购数量、申购类型是否有效
- 申购数量必须大于0

#### 用户验证
- 根据手机号查找用户，确保用户存在

#### 新股验证
- 验证新股代码是否存在
- 检查新股是否处于可申购状态（zt = 1）
- 检查新股是否被锁定（isLock != 1）

#### 价格计算
- 优先使用折扣价格（discountedPrice），如果没有则使用发行价格（price）
- 保证金 = 申购数量 × 价格

#### 扣款处理（仅type=2时）
- 检查用户可用余额是否足够
- 扣除用户可用余额
- 增加用户冻结资金（djzj）

#### 创建申购记录
- 生成唯一订单号
- 设置申购状态为待审核（status = 1）
- 记录备注为"后台管理员代为申购"

#### 异常处理
- 如果插入申购记录失败且已扣款，会自动回滚用户余额

## 使用示例

### 普通申购（不扣款）
```bash
curl -X POST "http://localhost:8093/admin/subscribe/addForUser.do" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "userPhone=13800138000&newCode=300001&applyNums=1000&type=1"
```

### 扣款申购
```bash
curl -X POST "http://localhost:8093/admin/subscribe/addForUser.do" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "userPhone=13800138000&newCode=300001&applyNums=1000&type=2"
```

### JavaScript 示例
```javascript
// 后台管理员帮用户申购新股
function adminAddNewStockForUser(userPhone, newCode, applyNums, type) {
    const formData = new FormData();
    formData.append('userPhone', userPhone);
    formData.append('newCode', newCode);
    formData.append('applyNums', applyNums);
    formData.append('type', type);
    
    fetch('/admin/subscribe/addForUser.do', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 0) {
            alert('申购成功：' + data.msg);
        } else {
            alert('申购失败：' + data.msg);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('请求失败');
    });
}

// 使用示例
adminAddNewStockForUser('13800138000', '300001', 1000, 2);
```

## 返回结果

### 成功响应
```json
{
    "status": 0,
    "msg": "代为申购成功，申购数量：1000，保证金：10000.00"
}
```

### 失败响应
```json
{
    "status": 1,
    "msg": "用户不存在，请检查手机号是否正确"
}
```

## 常见错误及解决方案

### 1. 参数错误
- **错误信息**：参数错误，请检查用户手机号、新股代码、申购数量和申购类型
- **解决方案**：检查所有必填参数是否正确传递

### 2. 用户不存在
- **错误信息**：用户不存在，请检查手机号是否正确
- **解决方案**：确认用户手机号是否在系统中存在

### 3. 新股代码不存在
- **错误信息**：新股代码不存在，请检查代码是否正确
- **解决方案**：确认新股代码是否正确，是否已在系统中配置

### 4. 新股不可申购
- **错误信息**：该新股当前不可申购 或 该新股已被锁定，不可申购
- **解决方案**：检查新股的状态设置，确保zt=1且isLock!=1

### 5. 用户余额不足
- **错误信息**：用户可用余额不足，当前余额：xxx，所需金额：xxx
- **解决方案**：确保用户有足够的可用余额，或选择type=1进行普通申购

## 注意事项

1. **权限控制**：此接口应该有适当的管理员权限验证
2. **日志记录**：建议记录管理员操作日志，包括操作人、操作时间、操作内容
3. **数据一致性**：扣款操作具有事务性，失败时会自动回滚
4. **申购限制**：可以根据业务需要添加申购数量限制、重复申购检查等
5. **审核流程**：申购记录创建后状态为待审核，需要后续的审核流程

## 扩展建议

1. 可以添加批量申购功能，支持为多个用户同时申购
2. 可以添加申购预检查接口，在实际申购前验证所有条件
3. 可以添加申购历史查询功能，方便管理员查看代为申购的记录
4. 可以考虑添加申购撤销功能，允许管理员撤销错误的申购
