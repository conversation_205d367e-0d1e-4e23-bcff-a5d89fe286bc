-- 创建用户资金来源表
CREATE TABLE `user_fund_source`
(
    `id`               int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id`          int(11) NOT NULL COMMENT '用户ID',
    `source_type`      varchar(64)    NOT NULL COMMENT '资金来源类型: RECHARGE(充值), POSITION_SALE(卖出股票)',
    `amount`           decimal(10, 2) NOT NULL COMMENT '原始金额',
    `remaining_amount` decimal(10, 2) NOT NULL COMMENT '剩余金额（未使用于购买的金额）',
    `create_time`      datetime       NOT NULL COMMENT '创建时间',
    `related_order_sn` varchar(50)  DEFAULT NULL COMMENT '关联的订单编号（充值单号或持仓单号）',
    `is_used`          tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已全部使用: 0-未全部使用, 1-已全部使用',
    `remark`           varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY                `idx_user_id` (`user_id`),
    KEY                `idx_source_type` (`source_type`),
    KEY                `idx_create_time` (`create_time`),
    KEY                `idx_is_used` (`is_used`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户资金来源表';
