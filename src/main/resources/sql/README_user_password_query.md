# 用户密码查询功能说明

## 概述
在后台用户列表中新增了根据用户ID查询用户密码的功能，支持查看用户的登录密码和提现密码，方便管理员进行用户支持和问题排查。

## 功能特性

### 1. 安全性
- 仅限管理员使用，接口位于 `/admin/` 路径下
- 返回的密码信息包含在安全的响应对象中
- 支持日志记录，便于审计

### 2. 完整性
- 显示用户基本信息（ID、手机号、真实姓名）
- 显示登录密码
- 显示两个提现密码字段（兼容历史数据）
- 提供有效提现密码的智能判断

### 3. 易用性
- 简单的接口调用
- 清晰的错误提示
- 友好的前端展示页面

## API 接口

### 接口信息
- **接口路径**: `/admin/user/getUserPasswords.do`
- **请求方法**: POST
- **接口描述**: 根据用户ID查询用户密码信息

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| userId | Integer | 是 | 用户ID |

### 响应格式
```json
{
    "status": 0,
    "msg": "查询成功",
    "data": {
        "userId": 1,
        "phone": "13800138000",
        "realName": "张三",
        "loginPassword": "userLoginPassword",
        "withdrawPassword": "oldWithdrawPassword",
        "withdrawalPassword": "newWithdrawPassword"
    }
}
```

### 错误响应示例
```json
{
    "status": 1,
    "msg": "用户不存在",
    "data": null
}
```

## 使用示例

### 1. jQuery Ajax 调用
```javascript
$.ajax({
    url: '/admin/user/getUserPasswords.do',
    type: 'POST',
    data: {
        userId: 123
    },
    success: function(response) {
        if (response.status === 0) {
            var data = response.data;
            console.log('用户信息:', data.realName, data.phone);
            console.log('登录密码:', data.loginPassword);
            console.log('提现密码:', data.withdrawalPassword || data.withdrawPassword);
        } else {
            alert('查询失败: ' + response.msg);
        }
    },
    error: function() {
        alert('请求失败，请重试');
    }
});
```

### 2. 表单提交
```html
<form action="/admin/user/getUserPasswords.do" method="post">
    <input type="number" name="userId" placeholder="请输入用户ID" required>
    <button type="submit">查询密码</button>
</form>
```

### 3. cURL 测试
```bash
curl -X POST "http://localhost:8080/admin/user/getUserPasswords.do" \
     -d "userId=123"
```

## 前端页面

### 页面文件
- **文件路径**: `src/main/resources/static/admin/user-password-query.html`
- **功能**: 提供用户友好的密码查询界面

### 页面特性
- 响应式设计，支持移动端
- 实时表单验证
- 清晰的结果展示
- 安全提醒信息
- 错误处理和用户反馈

### 页面截图说明
页面包含以下部分：
1. **安全提醒区域**: 提醒管理员谨慎操作
2. **查询表单**: 输入用户ID进行查询
3. **用户信息展示**: 显示用户基本信息
4. **密码信息展示**: 分别显示登录密码和提现密码

## 技术实现

### 1. 数据库层
```java
@Select("select id, phone, real_name, user_pwd, with_pwd, withdrawal_pwd from `user` where id = #{userId}")
User selectUserPasswordsById(@Param("userId") Integer userId);
```

### 2. 服务层
```java
public ServerResponse getUserPasswords(Integer userId) {
    // 参数验证
    if (userId == null) {
        return ServerResponse.createByErrorMsg("用户ID不能为空");
    }
    
    // 查询用户信息
    User user = userMapper.selectUserPasswordsById(userId);
    if (user == null) {
        return ServerResponse.createByErrorMsg("用户不存在");
    }
    
    // 构建返回对象
    UserPasswordVO passwordInfo = new UserPasswordVO();
    // ... 设置属性
    
    return ServerResponse.createBySuccess("查询成功", passwordInfo);
}
```

### 3. 控制层
```java
@RequestMapping({"getUserPasswords.do"})
@ResponseBody
public ServerResponse getUserPasswords(@RequestParam("userId") Integer userId) {
    return this.iUserService.getUserPasswords(userId);
}
```

### 4. VO 对象
```java
public class UserPasswordVO {
    private Integer userId;
    private String phone;
    private String realName;
    private String loginPassword;
    private String withdrawPassword;
    private String withdrawalPassword;
    
    // 智能获取有效的提现密码
    public String getEffectiveWithdrawPassword() {
        if (withdrawalPassword != null && !withdrawalPassword.trim().isEmpty()) {
            return withdrawalPassword;
        }
        return withdrawPassword;
    }
}
```

## 数据字段说明

### User 表相关字段
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Integer | 用户ID |
| phone | String | 手机号 |
| real_name | String | 真实姓名 |
| user_pwd | String | 登录密码 |
| with_pwd | String | 提现密码（旧字段） |
| withdrawal_pwd | String | 提现密码（新字段） |

### 密码字段兼容性
- `with_pwd`: 历史遗留的提现密码字段
- `withdrawal_pwd`: 新的提现密码字段
- 系统优先使用 `withdrawal_pwd`，如果为空则使用 `with_pwd`

## 安全考虑

### 1. 访问控制
- 接口仅限管理员访问
- 建议添加额外的权限验证
- 记录操作日志用于审计

### 2. 数据保护
- 密码信息仅在必要时查询
- 不在日志中记录敏感信息
- 前端页面添加安全提醒

### 3. 操作审计
```java
// 建议在查询密码时记录操作日志
log.info("管理员查询用户密码 - 用户ID: {}, 操作员: {}", userId, operatorId);
```

## 错误处理

### 常见错误及解决方案
| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| "用户ID不能为空" | 未提供userId参数 | 确保传递有效的用户ID |
| "用户不存在" | 用户ID在数据库中不存在 | 检查用户ID是否正确 |
| "查询失败：..." | 数据库异常 | 检查数据库连接和权限 |

## 测试验证

### 1. 单元测试
- 文件：`src/test/java/com/nq/service/UserPasswordServiceTest.java`
- 覆盖：成功查询、用户不存在、参数验证、异常处理等场景

### 2. 集成测试
```bash
# 测试正常查询
curl -X POST "http://localhost:8080/admin/user/getUserPasswords.do" -d "userId=1"

# 测试用户不存在
curl -X POST "http://localhost:8080/admin/user/getUserPasswords.do" -d "userId=99999"

# 测试参数缺失
curl -X POST "http://localhost:8080/admin/user/getUserPasswords.do"
```

## 部署清单

### 1. 代码部署
- [ ] 部署更新后的代码
- [ ] 重启应用服务

### 2. 功能验证
- [ ] 测试密码查询接口
- [ ] 验证前端页面显示
- [ ] 检查错误处理

### 3. 安全检查
- [ ] 确认权限控制生效
- [ ] 验证日志记录功能
- [ ] 检查敏感信息保护

## 使用场景

### 1. 用户支持
- 用户忘记密码时的快速查询
- 协助用户解决登录问题
- 提现密码相关问题排查

### 2. 系统维护
- 数据迁移时的密码验证
- 系统升级后的数据完整性检查
- 问题排查和调试

### 3. 审计需求
- 定期的密码安全检查
- 合规性审计支持
- 安全事件调查

## 扩展建议

### 1. 短期扩展
- 添加批量查询功能
- 支持按手机号查询
- 添加密码强度检查

### 2. 长期扩展
- 集成密码重置功能
- 添加密码历史记录
- 支持密码安全策略配置

### 3. 安全增强
- 添加操作员身份验证
- 实现敏感操作二次确认
- 集成安全审计系统
