# AgentUser serviceLine 字段完整功能说明

## 概述
为 AgentUser 实体添加 serviceLine（服务线）字段，并完善相关的代码，包括数据库表结构、实体类、Mapper 映射、批量更新接口等。

## 功能列表

### 1. 基础功能
- ✅ 数据库表添加 service_line 字段
- ✅ AgentUser 实体类添加 serviceLine 属性
- ✅ MyBatis 映射文件完整支持
- ✅ 单个代理用户的服务线 CRUD 操作

### 2. 批量更新功能
- ✅ 批量更新多个代理用户的服务线
- ✅ 参数验证和错误处理
- ✅ 事务安全保证

## 数据库修改

### 执行脚本
```sql
-- 文件: src/main/resources/sql/add_agent_user_service_line.sql
ALTER TABLE `agent_user` 
ADD COLUMN `service_line` VARCHAR(100) DEFAULT NULL COMMENT '服务线' AFTER `site_lever`;
```

## API 接口

### 1. 单个更新接口
**路径**: `/admin/agent/update.do`
**方法**: POST
**参数**: AgentUser 对象（包含 serviceLine 字段）

```javascript
// 更新单个代理的服务线
$.ajax({
    url: '/admin/agent/update.do',
    type: 'POST',
    data: {
        id: 1,
        serviceLine: 'VIP服务线'
    }
});
```

### 2. 批量更新接口
**路径**: `/admin/agent/batchUpdateServiceLine.do`
**方法**: POST
**参数**: 
- agentIds: List<Integer> - 代理ID列表
- serviceLine: String - 服务线名称

```javascript
// 批量更新多个代理的服务线
$.ajax({
    url: '/admin/agent/batchUpdateServiceLine.do',
    type: 'POST',
    data: {
        agentIds: [1, 2, 3, 4, 5],
        serviceLine: 'VIP服务线'
    },
    success: function(response) {
        if (response.status === 0) {
            alert('批量更新成功：' + response.msg);
        } else {
            alert('更新失败：' + response.msg);
        }
    }
});
```

## 代码使用示例

### Java 代码示例
```java
// 创建代理用户时设置服务线
AgentUser agentUser = new AgentUser();
agentUser.setAgentName("代理名称");
agentUser.setServiceLine("VIP服务线");
agentUserService.add(agentUser, request);

// 更新代理用户服务线
AgentUser updateAgent = new AgentUser();
updateAgent.setId(1);
updateAgent.setServiceLine("高级服务线");
agentUserService.update(updateAgent);

// 批量更新代理服务线
List<Integer> agentIds = Arrays.asList(1, 2, 3, 4, 5);
String serviceLine = "企业服务线";
ServerResponse result = agentUserService.batchUpdateServiceLine(agentIds, serviceLine);

// 获取代理用户服务线
AgentUser agent = agentUserService.findById(1);
String serviceLine = agent.getServiceLine();
```

### 前端表单示例
```html
<!-- 单个更新表单 -->
<form action="/admin/agent/update.do" method="post">
    <input type="hidden" name="id" value="1">
    <input type="text" name="serviceLine" placeholder="请输入服务线名称">
    <button type="submit">更新</button>
</form>

<!-- 批量更新表单 -->
<form action="/admin/agent/batchUpdateServiceLine.do" method="post">
    <input type="checkbox" name="agentIds" value="1"> 代理1
    <input type="checkbox" name="agentIds" value="2"> 代理2
    <input type="checkbox" name="agentIds" value="3"> 代理3
    <input type="text" name="serviceLine" placeholder="请输入服务线名称" required>
    <button type="submit">批量更新</button>
</form>
```

## 技术实现细节

### 1. 数据库层
- 字段类型：VARCHAR(100)
- 允许为空，不影响现有数据
- 位置：在 site_lever 字段之后

### 2. 实体层
- 添加 serviceLine 私有字段
- 提供 getter/setter 方法
- setter 方法包含 trim() 处理
- 更新构造函数支持新字段

### 3. 持久层
- 更新 MyBatis resultMap 映射
- 更新所有 SQL 语句（insert、update、select）
- 新增批量更新 SQL 语句

### 4. 业务层
- 单个更新：在 update 方法中添加 serviceLine 处理
- 批量更新：新增 batchUpdateServiceLine 方法
- 参数验证：检查 agentIds 和 serviceLine 有效性
- 异常处理：捕获并记录数据库异常

### 5. 控制层
- 复用现有的 update 接口支持单个更新
- 新增 batchUpdateServiceLine 接口支持批量更新
- 使用 @RequestParam 接收参数

## 错误处理

### 常见错误及解决方案
| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| "代理ID列表不能为空" | agentIds 参数为空 | 确保传递有效的代理ID列表 |
| "服务线不能为空" | serviceLine 参数为空 | 提供有效的服务线名称 |
| "没有找到需要更新的代理" | 代理ID不存在 | 检查代理ID是否正确 |
| "批量更新服务线失败" | 数据库异常 | 检查数据库连接和权限 |

## 测试验证

### 1. 单元测试
- 文件：`src/test/java/com/nq/controller/AdminAgentControllerTest.java`
- 覆盖：成功更新、参数验证、异常处理等场景

### 2. 集成测试建议
```bash
# 测试批量更新接口
curl -X POST "http://localhost:8080/admin/agent/batchUpdateServiceLine.do" \
     -d "agentIds=1&agentIds=2&agentIds=3&serviceLine=测试服务线"

# 测试单个更新接口
curl -X POST "http://localhost:8080/admin/agent/update.do" \
     -d "id=1&serviceLine=新服务线"
```

## 性能考虑

### 1. 批量更新优势
- 减少数据库连接次数
- 提高更新效率
- 保证事务一致性

### 2. 建议限制
- 单次批量更新建议不超过1000个代理
- 对于大量数据可考虑分批处理

## 安全考虑

### 1. 权限控制
- 接口位于 `/admin/` 路径下，需要管理员权限
- 建议添加操作日志记录

### 2. 数据验证
- 自动 trim() 处理输入
- 检查代理ID有效性
- 防止 SQL 注入（使用参数化查询）

## 扩展建议

### 1. 短期扩展
- 添加操作日志记录
- 支持按服务线查询代理
- 添加服务线统计功能

### 2. 长期扩展
- 创建服务线字典表
- 支持服务线层级管理
- 添加服务线相关的业务规则

## 部署清单

### 1. 数据库变更
- [ ] 执行 `add_agent_user_service_line.sql` 脚本
- [ ] 验证字段添加成功

### 2. 代码部署
- [ ] 部署更新后的代码
- [ ] 重启应用服务

### 3. 功能验证
- [ ] 测试单个代理更新功能
- [ ] 测试批量更新功能
- [ ] 验证数据完整性

### 4. 文档更新
- [ ] 更新 API 文档
- [ ] 更新用户操作手册
