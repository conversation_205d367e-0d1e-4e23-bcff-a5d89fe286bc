# UserBizService 用户业务服务说明

## 概述
新增了 UserBizService 用户业务处理类，专门处理复杂的用户业务逻辑。主要功能是为 UserServiceImpl 的 listByAdmin 方法提供增强版本，支持查询当前代理下所有代理的用户，返回的数据格式与原来的 listByAdmin 方法完全一致。

## 功能特性

### 1. 层级代理用户查询
- **功能**: 查询当前代理下所有代理的用户（包括直接下级和间接下级）
- **优势**: 相比原版只能查询直接下级，新版本支持多级代理结构
- **兼容性**: 返回数据格式与原 listByAdmin 方法完全一致

### 2. 完整的用户信息处理
- **用户签名**: 自动关联用户签名信息
- **财务数据**: 计算用户的持仓、盈亏、充值提现等财务信息
- **分页支持**: 完整的分页功能
- **搜索过滤**: 支持按姓名、手机号、账户类型、状态等条件过滤

## API 接口

### 接口信息
- **接口路径**: `/admin/userBiz/listByAdminWithSubAgents.do`
- **请求方法**: POST
- **功能**: 管理员查询用户列表（包含当前代理下所有代理的用户）

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| realName | String | 否 | - | 真实姓名（模糊查询） |
| phone | String | 否 | - | 手机号（模糊查询） |
| agentId | Integer | 否 | 当前代理ID | 代理ID，留空则使用当前登录代理 |
| accountType | Integer | 否 | - | 账户类型（0=真实，1=模拟） |
| pageNum | Integer | 否 | 1 | 页码 |
| pageSize | Integer | 否 | 10 | 每页大小 |
| status | Integer | 否 | - | 用户状态（0=未认证，1=认证中，2=已认证，3=认证失败） |

### 响应格式
```json
{
    "status": 0,
    "msg": "查询成功",
    "data": {
        "pageNum": 1,
        "pageSize": 10,
        "size": 2,
        "total": 2,
        "pages": 1,
        "list": [
            {
                "id": 1,
                "phone": "***********",
                "realName": "张三",
                "agentId": 1,
                "agentName": "代理A",
                "accountType": 0,
                "isActive": 2,
                "isLock": 0,
                "userAmt": 10000.00,
                "enableAmt": 8000.00,
                "currentPositionAmt": 9500.00,
                "totalPositionAmt": 9800.00,
                "totalBuyPrice": 5000.00,
                "regTime": "2024-01-01T10:00:00.000+00:00",
                "signatureMsg": "用户签名信息"
            }
        ]
    }
}
```

## 技术实现

### 1. 服务架构
```java
// 接口定义
public interface UserBizService {
    ServerResponse<PageInfo> listByAdminWithSubAgents(String realName, String phone, 
                                                     Integer agentId, Integer accountType, 
                                                     int pageNum, int pageSize, 
                                                     Integer status, HttpServletRequest request);
}

// 实现类
@Service("userBizService")
public class UserBizServiceImpl implements UserBizService {
    // 核心业务逻辑实现
}
```

### 2. 核心算法流程
```java
public ServerResponse<PageInfo> listByAdminWithSubAgents(...) {
    // 1. 获取当前代理用户
    AgentUser currentAgent = UserInfoUtil.getCurrentAgentUser(request);
    
    // 2. 获取所有下级代理ID列表（包含自己）
    List<Integer> agentIds = getSubordinateAgentIds(agentId);
    
    // 3. 查询用户列表
    List<User> users = userMapper.listByAdminWithAgentIds(realName, phone, agentIds, accountType, status);
    
    // 4. 处理用户签名信息
    processUserSignatures(users);
    
    // 5. 处理用户财务信息
    processUserFinancialInfo(users);
    
    // 6. 返回分页结果
    return ServerResponse.createBySuccess(new PageInfo<>(users));
}
```

### 3. 数据库查询
```xml
<!-- UserMapper.xml 中的新增查询 -->
<select id="listByAdminWithAgentIds" resultMap="BaseResultMap" parameterType="map">
    SELECT
    <include refid="Base_Column_List"/>
    FROM user
    <where>
        <if test="agentIds != null and agentIds.size() > 0">
            agent_id in
            <foreach collection="agentIds" item="agentId" open="(" separator="," close=")">
                #{agentId}
            </foreach>
        </if>
        <if test="realName != null and realName != ''">
            and real_name like CONCAT('%','${realName}','%')
        </if>
        <if test="phone != null and phone != ''">
            and phone like CONCAT('%','${phone}','%')
        </if>
        <if test="accountType != null">
            and account_type = #{accountType}
        </if>
        <if test="status != null">
            and is_active = #{status}
        </if>
    </where>
    ORDER BY id DESC
</select>
```

### 4. 财务信息计算
```java
private void processUserFinancialInfo(List<User> users) {
    for (User user : users) {
        // 获取用户持仓信息
        UserPositionDto userPositionDto = iUserPositionService.getUserPositionDto(user.getId());
        
        // 获取充值总额
        BigDecimal rechargeSumAmt = iUserRechargeService.sumUserRechargeAmt(user.getId());
        
        // 获取提现总额
        BigDecimal withdrawSumAmt = iUserWithdrawService.sumUserWithdrawAmt(user.getId());
        
        // 计算目前仓位 = 持仓盈亏 + 平仓盈利 + 充值 - 提现
        BigDecimal currentPositionAmt = rechargeSumAmt
                .subtract(withdrawSumAmt)
                .add(userPositionDto.getSumAllProfitAndLossAmt());
        
        // 计算总持仓 = 目前仓位 + 新股冻结盈利
        BigDecimal newStockProfitAmt = userStockSubscribeBizService.sumUserStockSubscribeProfit(user.getId());
        BigDecimal totalPositionAmt = currentPositionAmt.add(newStockProfitAmt);
        
        // 设置计算结果
        user.setTotalBuyPrice(userPositionDto.getSumPositionBuyAmt());
        user.setCurrentPositionAmt(currentPositionAmt);
        user.setTotalPositionAmt(totalPositionAmt);
    }
}
```

## 与原方法的对比

### 原 listByAdmin 方法
- **查询范围**: 只能查询指定代理的直接用户
- **代理限制**: 无法跨级查询下级代理的用户
- **使用场景**: 适合简单的单级代理结构

### 新 listByAdminWithSubAgents 方法
- **查询范围**: 查询当前代理下所有层级代理的用户
- **代理支持**: 支持多级代理结构，递归查询所有下级
- **数据一致**: 返回格式与原方法完全一致
- **功能增强**: 集成了 AgentUserBizService 的层级查询能力

### 数据格式兼容性
```java
// 原方法返回格式
ServerResponse<PageInfo> listByAdmin(String realName, String phone, Integer agentId, 
                                    Integer accountType, int pageNum, int pageSize, 
                                    Integer status, HttpServletRequest request)

// 新方法返回格式（完全一致）
ServerResponse<PageInfo> listByAdminWithSubAgents(String realName, String phone, Integer agentId, 
                                                 Integer accountType, int pageNum, int pageSize, 
                                                 Integer status, HttpServletRequest request)
```

## 前端集成

### 1. 演示页面
- **文件路径**: `src/main/resources/static/admin/user-list-with-sub-agents.html`
- **功能**: 提供完整的用户查询界面，支持多条件搜索和分页

### 2. 使用示例
```javascript
// 查询当前代理下所有用户
$.ajax({
    url: '/admin/userBiz/listByAdminWithSubAgents.do',
    type: 'POST',
    data: {
        realName: '张三',
        phone: '138',
        accountType: 0,
        status: 2,
        pageNum: 1,
        pageSize: 20
    },
    success: function(response) {
        if (response.status === 0) {
            displayUsers(response.data);
        }
    }
});
```

### 3. 前端特性
- **响应式设计**: 支持不同屏幕尺寸
- **实时搜索**: 支持多条件组合搜索
- **分页导航**: 完整的分页功能
- **数据展示**: 清晰的表格展示，包含状态标识
- **财务信息**: 格式化显示金额信息

## 使用场景

### 1. 多级代理管理
- **场景**: 总代理需要查看所有下级代理的用户
- **优势**: 一次查询获取完整的用户数据
- **效率**: 避免多次查询不同代理的用户

### 2. 业务统计分析
- **场景**: 统计整个代理体系的用户数据
- **功能**: 支持按各种条件筛选和分析
- **报表**: 为业务报表提供数据支持

### 3. 客户服务支持
- **场景**: 客服需要查询用户信息进行支持
- **便利**: 无需知道具体代理关系，直接搜索用户
- **效率**: 快速定位和处理用户问题

## 性能优化

### 1. 查询优化
```java
// 批量获取代理ID，减少数据库查询次数
List<Integer> agentIds = getSubordinateAgentIds(agentId);

// 使用 IN 查询，一次获取所有相关用户
List<User> users = userMapper.listByAdminWithAgentIds(realName, phone, agentIds, accountType, status);
```

### 2. 缓存策略
```java
// 可以考虑缓存代理层级关系
@Cacheable(value = "subordinateAgents", key = "#agentId")
private List<Integer> getSubordinateAgentIds(Integer agentId) {
    // 实现代码
}
```

### 3. 分页处理
```java
// 使用 PageHelper 进行分页，避免内存溢出
PageHelper.startPage(pageNum, pageSize);
List<User> users = userMapper.listByAdminWithAgentIds(...);
PageHelper.clearPage();
```

## 错误处理

### 常见错误及解决方案
| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| "无法获取当前代理用户信息" | 未登录或会话过期 | 重新登录 |
| "查询失败：..." | 数据库异常 | 检查数据库连接和权限 |
| "没有找到符合条件的用户" | 搜索条件过于严格 | 放宽搜索条件 |

## 测试验证

### 1. 单元测试
- **文件**: `src/test/java/com/nq/service/UserBizServiceTest.java`
- **覆盖**: 成功查询、参数验证、异常处理、边界条件等

### 2. 集成测试
```bash
# 测试基本查询
curl -X POST "http://localhost:8080/admin/userBiz/listByAdminWithSubAgents.do" \
     -d "pageNum=1&pageSize=10"

# 测试条件查询
curl -X POST "http://localhost:8080/admin/userBiz/listByAdminWithSubAgents.do" \
     -d "realName=张三&accountType=0&status=2&pageNum=1&pageSize=10"
```

### 3. 性能测试
- **数据量**: 测试大量用户数据的查询性能
- **并发**: 测试多用户同时查询的性能
- **内存**: 监控内存使用情况

## 部署清单

### 1. 代码部署
- [ ] 部署 UserBizService 接口和实现类
- [ ] 部署 AdminUserBizController 控制器
- [ ] 更新 UserMapper 接口和 XML 文件
- [ ] 重启应用服务

### 2. 数据库检查
- [ ] 确认相关表结构正确
- [ ] 验证查询性能
- [ ] 检查索引优化

### 3. 功能验证
- [ ] 测试用户查询接口
- [ ] 验证分页功能
- [ ] 检查数据完整性
- [ ] 测试前端页面

### 4. 性能监控
- [ ] 监控查询响应时间
- [ ] 检查内存使用情况
- [ ] 验证并发处理能力

## 扩展建议

### 1. 功能扩展
- 添加用户数据导出功能
- 支持更多搜索条件
- 集成用户操作日志

### 2. 性能扩展
- 实现查询结果缓存
- 添加异步查询支持
- 优化大数据量处理

### 3. 业务扩展
- 集成用户标签系统
- 添加用户分组功能
- 支持批量操作

这个新的 UserBizService 为用户管理提供了强大的多级代理查询能力，完全兼容原有的数据格式，可以无缝替换原有的查询方法，为复杂的代理业务场景提供了更好的支持。
