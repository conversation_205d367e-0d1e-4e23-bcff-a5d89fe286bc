-- 测试支付开关配置功能的SQL脚本

-- 1. 查看当前系统配置
SELECT 
    id,
    qiniu_domain,
    pay_enabled,
    pay_disabled_message
FROM sys_config;

-- 2. 测试关闭支付功能
UPDATE sys_config SET 
    pay_enabled = 0,
    pay_disabled_message = '系统维护中，支付功能暂时关闭，预计2小时后恢复，请稍后再试或联系客服'
WHERE id = 1;

-- 3. 验证配置更新
SELECT 
    id,
    pay_enabled,
    pay_disabled_message
FROM sys_config;

-- 4. 测试开启支付功能
UPDATE sys_config SET 
    pay_enabled = 1,
    pay_disabled_message = '支付功能暂时关闭，请稍后再试或联系客服'
WHERE id = 1;

-- 5. 最终验证
SELECT 
    id,
    pay_enabled,
    pay_disabled_message
FROM sys_config;

-- 6. 如果需要测试不同的提示信息，可以使用以下SQL
-- UPDATE sys_config SET pay_disabled_message = '由于系统升级，支付功能将在今晚22:00-次日06:00期间暂停服务，给您带来不便敬请谅解' WHERE id = 1;

-- 7. 查看最近的充值记录（用于测试充值接口是否正常工作）
SELECT 
    id,
    user_id,
    order_sn,
    pay_amt,
    order_status,
    add_time,
    pay_channel
FROM user_recharge 
ORDER BY add_time DESC 
LIMIT 10;
