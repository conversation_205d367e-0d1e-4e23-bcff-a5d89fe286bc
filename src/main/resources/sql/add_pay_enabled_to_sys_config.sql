-- 为 sys_config 表添加支付开关字段
ALTER TABLE sys_config
    ADD COLUMN pay_enabled INT DEFAULT 1 COMMENT '支付开关 0-关闭 1-开启';

-- 添加支付关闭时的提示信息字段
ALTER TABLE sys_config
    ADD COLUMN pay_disabled_message VARCHAR(500) DEFAULT '支付功能暂时关闭，请稍后再试或联系客服' COMMENT '支付关闭时的提示信息';

-- 添加充值次数限制相关字段
ALTER TABLE sys_config
    ADD COLUMN recharge_limit_enabled INT DEFAULT 0 COMMENT '充值次数限制开关 0-关闭 1-开启';
ALTER TABLE sys_config
    ADD COLUMN recharge_limit_daily INT DEFAULT 10 COMMENT '每日充值订单最大笔数(不论状态)';
ALTER TABLE sys_config
    ADD COLUMN recharge_limit_message VARCHAR(500) DEFAULT '您今日充值次数已达上限，请明日再试或联系客服' COMMENT '充值次数超限时的提示信息';

-- 如果表中没有记录，插入默认配置
INSERT INTO sys_config (qiniu_domain, pay_enabled, pay_disabled_message)
SELECT '',
       1,
       '支付功能暂时关闭，请稍后再试或联系客服' WHERE NOT EXISTS (SELECT 1 FROM sys_config);

-- 如果表中已有记录但没有 pay_enabled 字段值，更新为默认值
UPDATE sys_config
SET pay_enabled = 1
WHERE pay_enabled IS NULL;
UPDATE sys_config
SET pay_disabled_message = '支付功能暂时关闭，请稍后再试或联系客服'
WHERE pay_disabled_message IS NULL;
