# 支付开关配置功能说明

## 概述
在初始化接口 `getInitConfig` 中添加了支付开关配置，后台管理员可以通过配置接口控制支付功能的开启和关闭。

## 功能特性

### 1. 数据库字段

#### 支付开关字段
- 表名：`sys_config`
- 字段名：`pay_enabled`
- 字段类型：`INT`
- 默认值：`1`（开启）
- 说明：`0-关闭 1-开启`

#### 支付关闭提示信息字段
- 表名：`sys_config`
- 字段名：`pay_disabled_message`
- 字段类型：`VARCHAR(500)`
- 默认值：`支付功能暂时关闭，请稍后再试或联系客服`
- 说明：支付关闭时向用户显示的提示信息

### 2. 前端接口
#### 获取初始化配置
- **接口路径**：`/system/getInitConfig`
- **请求方式**：`POST`
- **返回数据**：包含 `payEnabled` 字段的系统配置对象
- **加密**：使用 `@Encrypt` 注解加密返回数据

#### 后台管理接口
- **获取配置**：`/admin/system/getInitConfig`
- **更新配置**：`/admin/system/updateConfig`

#### 充值接口支付开关判断
- **接口路径**：`/user/recharge/inMoney.do`
- **功能**：在用户发起充值请求时，优先检查支付开关状态
- **逻辑**：如果支付关闭（payEnabled=0），直接返回配置的提示信息

### 3. 后端实现

#### 实体类
```java
@Data
@TableName("sys_config")
public class SysConfig implements Serializable {
    @TableId(type = IdType.AUTO, value = "id")
    private Integer id;

    /**
     * 七牛域名
     */
    private String qiniuDomain;

    /**
     * 支付开关 0-关闭 1-开启
     */
    private Integer payEnabled;

    /**
     * 支付关闭时的提示信息
     */
    private String payDisabledMessage;
}
```

#### 服务接口
```java
public interface SysConfigService {
    SysConfig getSysConfig();

    /**
     * 更新系统配置
     * @param sysConfig 系统配置
     * @return 更新结果
     */
    int updateSysConfig(SysConfig sysConfig);
}
```

#### 后台管理控制器
```java
@RequestMapping("updateConfig")
public ServerResponse updateConfig(SysConfig sysConfig) {
    log.info("更新系统配置, sysConfig={}", sysConfig);
    int result = sysConfigService.updateSysConfig(sysConfig);
    if (result > 0) {
        return ServerResponse.createBySuccessMsg("配置更新成功");
    }
    return ServerResponse.createByErrorMsg("配置更新失败");
}
```

#### 充值接口支付开关判断
```java
public ServerResponse inMoney(String amt, String payType, String password, HttpServletRequest request) {
    if (StringUtils.isBlank(amt)) {
        return ServerResponse.createByErrorMsg("参数不能为空");
    }

    // 检查支付开关
    SysConfig sysConfig = sysConfigService.getSysConfig();
    if (sysConfig != null && sysConfig.getPayEnabled() != null && sysConfig.getPayEnabled() == 0) {
        // 支付已关闭，返回配置的提示信息
        String message = sysConfig.getPayDisabledMessage();
        if (StringUtils.isBlank(message)) {
            message = "支付功能暂时关闭，请稍后再试或联系客服";
        }
        return ServerResponse.createByErrorMsg(message);
    }

    // 继续原有的充值逻辑...
}
```

## 部署步骤

### 1. 执行数据库脚本
```sql
-- 执行以下 SQL 脚本
source src/main/resources/sql/add_pay_enabled_to_sys_config.sql
```

### 2. 重启应用
重启 Java 应用以加载新的代码更改。

### 3. 验证功能
1. 调用前端接口 `/system/getInitConfig` 验证返回数据包含 `payEnabled` 字段
2. 调用后台接口 `/admin/system/getInitConfig` 获取当前配置
3. 调用后台接口 `/admin/system/updateConfig` 测试配置更新

## 使用示例

### 前端获取支付开关状态
```javascript
// 调用初始化接口
fetch('/system/getInitConfig', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => {
    if (data.status === 0) {
        const payEnabled = data.data.payEnabled;
        // 根据 payEnabled 值控制支付功能显示
        if (payEnabled === 1) {
            // 显示支付功能
        } else {
            // 隐藏支付功能
        }
    }
});
```

### 后台更新支付开关
```javascript
// 更新支付开关配置
fetch('/admin/system/updateConfig', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: 'id=1&payEnabled=0&payDisabledMessage=系统维护中，请稍后再试' // 关闭支付功能并设置提示信息
})
.then(response => response.json())
.then(data => {
    if (data.status === 0) {
        alert('配置更新成功');
    } else {
        alert('配置更新失败：' + data.msg);
    }
});
```

### 用户充值时的支付开关检查
```javascript
// 用户发起充值请求
fetch('/user/recharge/inMoney.do', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        amt: '100',
        payType: '1',
        password: '123456'
    })
})
.then(response => response.json())
.then(data => {
    if (data.status === 0) {
        // 充值成功，跳转到支付页面
        window.location.href = data.data;
    } else {
        // 充值失败，显示错误信息（可能是支付关闭的提示）
        alert(data.msg);
    }
});
```

## 注意事项

1. **权限控制**：后台配置接口应该有适当的权限验证
2. **缓存更新**：如果使用了缓存，更新配置后需要清除相关缓存
3. **前端适配**：前端需要根据 `payEnabled` 字段动态控制支付相关功能的显示和隐藏
4. **默认值**：新安装的系统默认开启支付功能（`payEnabled = 1`）

## 扩展建议

1. 可以考虑添加更多的开关配置，如充值开关、提现开关等
2. 可以添加配置变更日志记录功能
3. 可以考虑实时生效，无需重启应用
