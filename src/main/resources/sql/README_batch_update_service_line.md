# 批量更新代理服务线接口说明

## 概述
在 AdminAgentController 中新增了批量更新代理服务线的接口，支持一次性为多个代理用户设置相同的服务线。

## 新增接口

### 接口信息
- **接口路径**: `/admin/agent/batchUpdateServiceLine.do`
- **请求方法**: POST
- **接口描述**: 批量更新代理用户的服务线

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| agentIds | List<Integer> | 是 | 代理ID列表，支持多个代理ID |
| serviceLine | String | 是 | 要设置的服务线名称 |

### 响应格式
```json
{
    "status": 0,
    "msg": "批量更新服务线成功，共更新3个代理",
    "data": null
}
```

### 错误响应示例
```json
{
    "status": 1,
    "msg": "代理ID列表不能为空",
    "data": null
}
```

## 使用示例

### 前端调用示例
```javascript
// 使用 jQuery
$.ajax({
    url: '/admin/agent/batchUpdateServiceLine.do',
    type: 'POST',
    data: {
        agentIds: [1, 2, 3, 4, 5],  // 代理ID数组
        serviceLine: 'VIP服务线'      // 服务线名称
    },
    success: function(response) {
        if (response.status === 0) {
            alert('批量更新成功：' + response.msg);
        } else {
            alert('更新失败：' + response.msg);
        }
    },
    error: function() {
        alert('请求失败，请重试');
    }
});

// 使用 fetch API
fetch('/admin/agent/batchUpdateServiceLine.do', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
        agentIds: [1, 2, 3],
        serviceLine: '高级服务线'
    })
})
.then(response => response.json())
.then(data => {
    console.log('更新结果:', data);
});
```

### 表单提交示例
```html
<form action="/admin/agent/batchUpdateServiceLine.do" method="post">
    <!-- 多选代理ID -->
    <input type="checkbox" name="agentIds" value="1"> 代理1
    <input type="checkbox" name="agentIds" value="2"> 代理2
    <input type="checkbox" name="agentIds" value="3"> 代理3
    
    <!-- 服务线输入 -->
    <input type="text" name="serviceLine" placeholder="请输入服务线名称" required>
    
    <button type="submit">批量更新</button>
</form>
```

### cURL 测试示例
```bash
curl -X POST "http://localhost:8080/admin/agent/batchUpdateServiceLine.do" \
     -d "agentIds=1&agentIds=2&agentIds=3&serviceLine=VIP服务线"
```

## 实现细节

### 数据库层面
- 使用 MyBatis 的 `<foreach>` 标签实现批量更新
- SQL 语句：`UPDATE agent_user SET service_line = ? WHERE id IN (?, ?, ?)`
- 支持事务，确保数据一致性

### 业务逻辑
1. **参数验证**：
   - 检查代理ID列表是否为空
   - 检查服务线名称是否为空
   - 自动对服务线名称进行 trim() 处理

2. **执行更新**：
   - 调用 Mapper 层的批量更新方法
   - 返回实际更新的记录数

3. **异常处理**：
   - 捕获数据库异常
   - 记录错误日志
   - 返回友好的错误信息

### 返回信息
- **成功**：返回更新成功的代理数量
- **失败**：返回具体的错误原因

## 注意事项

1. **权限控制**：该接口位于 `/admin/` 路径下，需要管理员权限
2. **数据验证**：
   - 代理ID必须存在于数据库中
   - 服务线名称会自动去除首尾空格
3. **性能考虑**：
   - 批量更新比逐个更新效率更高
   - 建议单次更新的代理数量不超过1000个
4. **事务安全**：整个批量更新操作在一个事务中执行

## 错误码说明

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| "代理ID列表不能为空" | agentIds 参数为空或空列表 | 确保传递有效的代理ID列表 |
| "服务线不能为空" | serviceLine 参数为空或空字符串 | 提供有效的服务线名称 |
| "没有找到需要更新的代理" | 提供的代理ID在数据库中不存在 | 检查代理ID是否正确 |
| "批量更新服务线失败：..." | 数据库操作异常 | 检查数据库连接和SQL语句 |

## 扩展建议

1. **添加权限验证**：确保只有有权限的管理员可以执行批量更新
2. **添加操作日志**：记录谁在什么时间对哪些代理进行了批量更新
3. **添加数据校验**：验证代理ID是否存在，避免无效更新
4. **支持更多字段**：可以扩展为批量更新其他字段的通用接口
