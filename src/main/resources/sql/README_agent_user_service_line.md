# AgentUser 添加 serviceLine 字段修改说明

## 概述
为 AgentUser 实体添加 serviceLine（服务线）字段，并完善相关的代码，包括数据库表结构、实体类、Mapper 映射等。

## 修改内容

### 1. 数据库表结构修改
**文件**: `src/main/resources/sql/add_agent_user_service_line.sql`
- 为 `agent_user` 表添加 `service_line` 字段
- 字段类型：VARCHAR(100)，允许为空
- 字段注释：服务线

### 2. 实体类修改
**文件**: `src/main/java/com/nq/pojo/AgentUser.java`
- 添加 `serviceLine` 私有字段
- 更新构造函数，添加 `serviceLine` 参数
- 添加 `getServiceLine()` 和 `setServiceLine()` 方法
- `setServiceLine()` 方法包含 trim() 处理，与其他字符串字段保持一致

### 3. MyBatis 映射文件修改
**文件**: `src/main/resources/mapper/AgentUserMapper.xml`
- 更新 `BaseResultMap` 的构造函数映射，添加 `service_line` 字段
- 更新 `Base_Column_List`，添加 `service_line` 字段
- 更新 `insert` 语句，添加 `service_line` 字段和参数
- 更新 `insertSelective` 语句，添加 `service_line` 的条件判断和参数
- 更新 `updateByPrimaryKeySelective` 语句，添加 `service_line` 的条件更新
- 更新 `updateByPrimaryKey` 语句，添加 `service_line` 字段

### 4. 服务层修改
**文件**: `src/main/java/com/nq/service/impl/AgentUserServiceImpl.java`
- 在 `update()` 方法中添加对 `serviceLine` 字段的处理
- 支持通过 AgentUser 对象更新 serviceLine 字段

## 使用方式

### 1. 执行数据库脚本
```sql
-- 执行以下脚本为表添加字段
source src/main/resources/sql/add_agent_user_service_line.sql
```

### 2. 在代码中使用
```java
// 创建代理用户时设置服务线
AgentUser agentUser = new AgentUser();
agentUser.setServiceLine("VIP服务线");

// 更新代理用户服务线
AgentUser updateAgent = new AgentUser();
updateAgent.setId(agentId);
updateAgent.setServiceLine("高级服务线");
agentUserService.update(updateAgent);

// 获取代理用户服务线
String serviceLine = agentUser.getServiceLine();
```

### 3. 前端接口调用
在添加或更新代理用户时，可以通过以下方式传递 serviceLine 参数：

**添加代理用户**（AdminAgentController.add）:
```javascript
// POST /backend/agent/add.do
{
    "agentName": "代理名称",
    "agentPhone": "手机号",
    "agentRealName": "真实姓名",
    "agentPwd": "密码",
    "serviceLine": "服务线名称",
    // 其他字段...
}
```

**更新代理用户**（AdminAgentController.update）:
```javascript
// POST /backend/agent/update.do
{
    "id": 1,
    "serviceLine": "新的服务线名称",
    // 其他需要更新的字段...
}
```

## 注意事项

1. **数据库兼容性**: 新字段允许为空，不会影响现有数据
2. **向后兼容**: 所有现有的 API 接口保持兼容，serviceLine 字段为可选
3. **数据验证**: serviceLine 字段会自动进行 trim() 处理
4. **查询优化**: 如果需要按服务线查询，可以考虑为该字段添加索引

## 测试建议

1. **数据库测试**: 验证字段添加成功，现有数据不受影响
2. **CRUD 测试**: 测试代理用户的增删改查操作
3. **API 测试**: 测试前端接口的添加和更新功能
4. **兼容性测试**: 确保不传递 serviceLine 参数时系统正常工作

## 后续扩展

如果需要进一步扩展服务线功能，可以考虑：
1. 创建服务线字典表，规范服务线名称
2. 添加服务线相关的查询和统计功能
3. 在用户管理中显示代理的服务线信息
4. 添加按服务线筛选代理用户的功能
