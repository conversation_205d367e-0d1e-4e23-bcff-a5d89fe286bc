# 支付开关功能测试指南

## 测试概述
本文档提供了支付开关功能的完整测试流程，包括数据库配置、接口测试和功能验证。

## 测试环境准备

### 1. 执行数据库脚本
```sql
-- 执行支付开关配置脚本
source src/main/resources/sql/add_pay_enabled_to_sys_config.sql
```

### 2. 验证数据库表结构
```sql
-- 检查 sys_config 表结构
DESCRIBE sys_config;

-- 查看当前配置
SELECT * FROM sys_config;
```

## 功能测试步骤

### 测试场景1：支付开关开启状态（默认）

#### 1.1 验证初始配置
```bash
# 调用初始化接口
curl -X POST "http://localhost:8093/system/getInitConfig" \
  -H "Content-Type: application/json"
```

**期望结果**：
```json
{
    "status": 0,
    "data": {
        "id": 1,
        "qiniuDomain": "",
        "payEnabled": 1,
        "payDisabledMessage": "支付功能暂时关闭，请稍后再试或联系客服"
    }
}
```

#### 1.2 测试充值接口（支付开启）
```bash
# 模拟用户充值请求
curl -X POST "http://localhost:8093/user/recharge/inMoney.do" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "amt": "100",
    "payType": "1",
    "password": "123456"
  }'
```

**期望结果**：正常进入充值流程，不会被支付开关拦截。

### 测试场景2：关闭支付功能

#### 2.1 通过后台接口关闭支付
```bash
# 关闭支付功能
curl -X POST "http://localhost:8093/admin/system/updateConfig" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "id=1&payEnabled=0&payDisabledMessage=系统维护中，支付功能暂时关闭，预计2小时后恢复"
```

**期望结果**：
```json
{
    "status": 0,
    "msg": "配置更新成功"
}
```

#### 2.2 验证配置更新
```bash
# 再次调用初始化接口
curl -X POST "http://localhost:8093/system/getInitConfig" \
  -H "Content-Type: application/json"
```

**期望结果**：
```json
{
    "status": 0,
    "data": {
        "id": 1,
        "qiniuDomain": "",
        "payEnabled": 0,
        "payDisabledMessage": "系统维护中，支付功能暂时关闭，预计2小时后恢复"
    }
}
```

#### 2.3 测试充值接口（支付关闭）
```bash
# 再次模拟用户充值请求
curl -X POST "http://localhost:8093/user/recharge/inMoney.do" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "amt": "100",
    "payType": "1",
    "password": "123456"
  }'
```

**期望结果**：
```json
{
    "status": 1,
    "msg": "系统维护中，支付功能暂时关闭，预计2小时后恢复"
}
```

### 测试场景3：自定义提示信息

#### 3.1 设置自定义提示信息
```bash
# 设置自定义提示信息
curl -X POST "http://localhost:8093/admin/system/updateConfig" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "id=1&payEnabled=0&payDisabledMessage=由于系统升级，支付功能将在今晚22:00-次日06:00期间暂停服务，给您带来不便敬请谅解"
```

#### 3.2 验证自定义提示信息
```bash
# 测试充值接口
curl -X POST "http://localhost:8093/user/recharge/inMoney.do" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "amt": "100",
    "payType": "1",
    "password": "123456"
  }'
```

**期望结果**：
```json
{
    "status": 1,
    "msg": "由于系统升级，支付功能将在今晚22:00-次日06:00期间暂停服务，给您带来不便敬请谅解"
}
```

### 测试场景4：重新开启支付功能

#### 4.1 开启支付功能
```bash
# 重新开启支付功能
curl -X POST "http://localhost:8093/admin/system/updateConfig" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "id=1&payEnabled=1"
```

#### 4.2 验证支付功能恢复
```bash
# 测试充值接口
curl -X POST "http://localhost:8093/user/recharge/inMoney.do" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "amt": "100",
    "payType": "1",
    "password": "123456"
  }'
```

**期望结果**：正常进入充值流程。

## 边界情况测试

### 测试场景5：空提示信息处理

#### 5.1 设置空提示信息
```sql
-- 直接通过SQL设置空提示信息
UPDATE sys_config SET 
    pay_enabled = 0,
    pay_disabled_message = NULL 
WHERE id = 1;
```

#### 5.2 测试默认提示信息
```bash
# 测试充值接口
curl -X POST "http://localhost:8093/user/recharge/inMoney.do" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "amt": "100",
    "payType": "1",
    "password": "123456"
  }'
```

**期望结果**：
```json
{
    "status": 1,
    "msg": "支付功能暂时关闭，请稍后再试或联系客服"
}
```

### 测试场景6：配置不存在的情况

#### 6.1 清空配置表
```sql
-- 备份当前配置
CREATE TABLE sys_config_backup AS SELECT * FROM sys_config;

-- 清空配置表
DELETE FROM sys_config;
```

#### 6.2 测试充值接口
```bash
# 测试充值接口
curl -X POST "http://localhost:8093/user/recharge/inMoney.do" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "amt": "100",
    "payType": "1",
    "password": "123456"
  }'
```

**期望结果**：正常进入充值流程（因为配置不存在时，不会拦截支付）。

#### 6.3 恢复配置
```sql
-- 恢复配置
INSERT INTO sys_config SELECT * FROM sys_config_backup;
DROP TABLE sys_config_backup;
```

## 前端集成测试

### JavaScript 测试代码
```html
<!DOCTYPE html>
<html>
<head>
    <title>支付开关测试</title>
</head>
<body>
    <h1>支付开关功能测试</h1>
    
    <div>
        <h2>1. 获取当前配置</h2>
        <button onclick="getConfig()">获取配置</button>
        <div id="configResult"></div>
    </div>
    
    <div>
        <h2>2. 更新支付开关</h2>
        <label>
            <input type="radio" name="payEnabled" value="1" checked> 开启支付
        </label>
        <label>
            <input type="radio" name="payEnabled" value="0"> 关闭支付
        </label>
        <br>
        <label>提示信息：</label>
        <input type="text" id="payMessage" value="支付功能暂时关闭，请稍后再试或联系客服" style="width: 400px;">
        <br>
        <button onclick="updateConfig()">更新配置</button>
        <div id="updateResult"></div>
    </div>
    
    <div>
        <h2>3. 测试充值接口</h2>
        <button onclick="testRecharge()">测试充值</button>
        <div id="rechargeResult"></div>
    </div>

    <script>
        function getConfig() {
            fetch('/system/getInitConfig', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('configResult').innerHTML = 
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<span style="color: red;">错误: ' + error + '</span>';
            });
        }
        
        function updateConfig() {
            const payEnabled = document.querySelector('input[name="payEnabled"]:checked').value;
            const payMessage = document.getElementById('payMessage').value;
            
            const formData = new FormData();
            formData.append('id', '1');
            formData.append('payEnabled', payEnabled);
            formData.append('payDisabledMessage', payMessage);
            
            fetch('/admin/system/updateConfig', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('updateResult').innerHTML = 
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                document.getElementById('updateResult').innerHTML = 
                    '<span style="color: red;">错误: ' + error + '</span>';
            });
        }
        
        function testRecharge() {
            fetch('/user/recharge/inMoney.do', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    amt: '100',
                    payType: '1',
                    password: '123456'
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('rechargeResult').innerHTML = 
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                document.getElementById('rechargeResult').innerHTML = 
                    '<span style="color: red;">错误: ' + error + '</span>';
            });
        }
    </script>
</body>
</html>
```

## 测试检查清单

- [ ] 数据库脚本执行成功
- [ ] 初始化接口返回支付开关配置
- [ ] 支付开启时充值接口正常工作
- [ ] 支付关闭时充值接口返回配置的提示信息
- [ ] 后台接口可以成功更新支付开关配置
- [ ] 自定义提示信息正确显示
- [ ] 空提示信息时使用默认提示
- [ ] 配置不存在时不影响正常功能
- [ ] 前端可以正确获取和显示支付开关状态

## 常见问题排查

### 1. 充值接口没有被拦截
- 检查 SysConfigService 是否正确注入
- 检查数据库中 pay_enabled 字段值
- 检查日志中是否有异常信息

### 2. 提示信息不正确
- 检查数据库中 pay_disabled_message 字段值
- 确认字段长度是否足够（VARCHAR(500)）

### 3. 配置更新失败
- 检查后台接口权限
- 确认参数传递是否正确
- 检查数据库连接和更新权限

### 4. 前端获取不到配置
- 检查接口加密/解密是否正常
- 确认返回数据格式是否正确
