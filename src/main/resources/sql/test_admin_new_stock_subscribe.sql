-- 测试后台管理员帮用户申购新股功能的SQL脚本

-- 1. 查看现有的新股信息
SELECT 
    newlist_id,
    name,
    code,
    stock_type,
    price,
    discounted_price,
    zt,
    is_lock,
    subscribe_time,
    subscription_time
FROM stock_subscribe 
WHERE zt = 1 AND (is_lock IS NULL OR is_lock != 1)
ORDER BY newlist_id DESC
LIMIT 5;

-- 2. 查看测试用户信息（可以用于测试）
SELECT 
    id,
    phone,
    real_name,
    enable_amt,
    djzj,
    is_lock
FROM user 
WHERE phone IN ('***********', '13800138001', '13800138002')
ORDER BY id;

-- 3. 查看最近的申购记录
SELECT 
    id,
    order_no,
    user_id,
    real_name,
    phone,
    new_code,
    new_name,
    apply_nums,
    bond,
    buy_price,
    type,
    status,
    add_time,
    remarks
FROM user_stock_subscribe 
ORDER BY add_time DESC
LIMIT 10;

-- 4. 如果需要创建测试数据，可以使用以下SQL

-- 创建测试新股（如果不存在）
INSERT INTO stock_subscribe (
    name, 
    code, 
    stock_type, 
    price, 
    discounted_price,
    order_number,
    zt, 
    is_lock, 
    subscribe_time, 
    subscription_time,
    type,
    winning_rate
) VALUES (
    '测试新股001', 
    'TEST001', 
    'sh', 
    10.00, 
    9.50,
    1000000,
    1, 
    0, 
    NOW(), 
    DATE_ADD(NOW(), INTERVAL 7 DAY),
    1,
    10
) ON DUPLICATE KEY UPDATE name = name;

-- 创建测试用户（如果不存在）
INSERT INTO user (
    phone,
    user_pwd,
    nick_name,
    real_name,
    enable_amt,
    djzj,
    is_lock,
    reg_time,
    account_type
) VALUES (
    '***********',
    'e10adc3949ba59abbe56e057f20f883e', -- 123456的MD5
    '测试用户001',
    '张三',
    50000.00,
    0.00,
    0,
    NOW(),
    1
) ON DUPLICATE KEY UPDATE real_name = real_name;

-- 5. 测试完成后，可以查看申购结果
-- SELECT * FROM user_stock_subscribe WHERE remarks = '后台管理员代为申购' ORDER BY add_time DESC LIMIT 5;

-- 6. 查看用户余额变化（在执行申购前后对比）
-- SELECT id, phone, real_name, enable_amt, djzj FROM user WHERE phone = '***********';
