<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理用户树形结构演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="number"] {
            width: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .tree-container {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            max-height: 500px;
            overflow-y: auto;
        }
        .tree-node {
            margin: 5px 0;
            padding: 8px;
            background-color: white;
            border-left: 4px solid #007bff;
            border-radius: 0 4px 4px 0;
        }
        .tree-node.level-0 { border-left-color: #dc3545; margin-left: 0px; }
        .tree-node.level-1 { border-left-color: #fd7e14; margin-left: 20px; }
        .tree-node.level-2 { border-left-color: #ffc107; margin-left: 40px; }
        .tree-node.level-3 { border-left-color: #28a745; margin-left: 60px; }
        .tree-node.level-4 { border-left-color: #17a2b8; margin-left: 80px; }
        .tree-node.level-5 { border-left-color: #6f42c1; margin-left: 100px; }
        .tree-node.level-6 { border-left-color: #e83e8c; margin-left: 120px; }
        
        .agent-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .agent-basic {
            flex: 1;
        }
        .agent-stats {
            color: #666;
            font-size: 0.9em;
        }
        .agent-name {
            font-weight: bold;
            color: #333;
        }
        .agent-details {
            color: #666;
            font-size: 0.9em;
            margin-top: 2px;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .stats-summary {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .stats-item {
            display: inline-block;
            margin-right: 20px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>代理用户树形结构演示</h1>
        
        <!-- 获取下级代理 -->
        <div class="section">
            <h2>1. 获取下级代理用户</h2>
            <form id="subordinateForm">
                <div class="form-group">
                    <label for="agentId">代理ID：</label>
                    <input type="number" id="agentId" name="agentId" placeholder="请输入代理ID" required>
                    <button type="submit">查询下级代理</button>
                </div>
            </form>
            
            <div id="subordinateResult" class="result">
                <div id="subordinateMessage"></div>
                <div id="subordinateStats" class="stats-summary" style="display: none;"></div>
                <div id="subordinateTree" class="tree-container" style="display: none;"></div>
            </div>
        </div>
        
        <!-- 获取完整树形结构 -->
        <div class="section">
            <h2>2. 完整代理树形结构</h2>
            <button id="loadTreeBtn">加载完整代理树</button>
            
            <div id="treeResult" class="result">
                <div id="treeMessage"></div>
                <div id="treeStats" class="stats-summary" style="display: none;"></div>
                <div id="treeContainer" class="tree-container" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // 获取下级代理
            $('#subordinateForm').on('submit', function(e) {
                e.preventDefault();
                
                var agentId = $('#agentId').val();
                if (!agentId) {
                    showResult('subordinate', 'error', '请输入代理ID');
                    return;
                }
                
                showLoading('subordinate');
                
                $.ajax({
                    url: '/admin/agentBiz/getSubordinateAgents.do',
                    type: 'POST',
                    data: { agentId: agentId },
                    success: function(response) {
                        if (response.status === 0) {
                            showSubordinateAgents(response.data);
                            showResult('subordinate', 'success', response.msg);
                        } else {
                            showResult('subordinate', 'error', response.msg);
                            hideTree('subordinate');
                        }
                    },
                    error: function(xhr, status, error) {
                        showResult('subordinate', 'error', '请求失败：' + error);
                        hideTree('subordinate');
                    }
                });
            });
            
            // 加载完整树形结构
            $('#loadTreeBtn').on('click', function() {
                showLoading('tree');
                
                $.ajax({
                    url: '/admin/agentBiz/getAgentTreeStructure.do',
                    type: 'POST',
                    success: function(response) {
                        if (response.status === 0) {
                            showAgentTree(response.data);
                            showResult('tree', 'success', response.msg);
                        } else {
                            showResult('tree', 'error', response.msg);
                            hideTree('tree');
                        }
                    },
                    error: function(xhr, status, error) {
                        showResult('tree', 'error', '请求失败：' + error);
                        hideTree('tree');
                    }
                });
            });
        });
        
        function showLoading(type) {
            var resultDiv = $('#' + type + 'Result');
            var messageDiv = $('#' + type + 'Message');
            
            resultDiv.removeClass('success error').addClass('success');
            messageDiv.html('<div class="loading">正在加载...</div>');
            resultDiv.show();
            hideTree(type);
        }
        
        function showResult(type, status, message) {
            var resultDiv = $('#' + type + 'Result');
            var messageDiv = $('#' + type + 'Message');
            
            resultDiv.removeClass('success error').addClass(status);
            messageDiv.text(message);
            resultDiv.show();
        }
        
        function hideTree(type) {
            $('#' + type + 'Stats').hide();
            $('#' + (type === 'subordinate' ? 'subordinateTree' : 'treeContainer')).hide();
        }
        
        function showSubordinateAgents(agents) {
            var statsDiv = $('#subordinateStats');
            var treeDiv = $('#subordinateTree');
            
            // 显示统计信息
            var levelStats = {};
            agents.forEach(function(agent) {
                var level = agent.agentLevel || 0;
                levelStats[level] = (levelStats[level] || 0) + 1;
            });
            
            var statsHtml = '<div class="stats-item">总数量: ' + agents.length + '</div>';
            for (var level in levelStats) {
                statsHtml += '<div class="stats-item">级别' + level + ': ' + levelStats[level] + '个</div>';
            }
            statsDiv.html(statsHtml);
            statsDiv.show();
            
            // 显示代理列表
            var html = '';
            agents.forEach(function(agent) {
                html += renderAgentNode(agent, false);
            });
            
            treeDiv.html(html);
            treeDiv.show();
        }
        
        function showAgentTree(treeData) {
            var statsDiv = $('#treeStats');
            var treeDiv = $('#treeContainer');
            
            // 计算统计信息
            var totalCount = 0;
            var levelStats = {};
            
            function countNodes(nodes) {
                nodes.forEach(function(node) {
                    totalCount++;
                    var level = node.agentLevel || 0;
                    levelStats[level] = (levelStats[level] || 0) + 1;
                    
                    if (node.children && node.children.length > 0) {
                        countNodes(node.children);
                    }
                });
            }
            
            countNodes(treeData);
            
            // 显示统计信息
            var statsHtml = '<div class="stats-item">总数量: ' + totalCount + '</div>';
            for (var level in levelStats) {
                statsHtml += '<div class="stats-item">级别' + level + ': ' + levelStats[level] + '个</div>';
            }
            statsDiv.html(statsHtml);
            statsDiv.show();
            
            // 渲染树形结构
            var html = '';
            treeData.forEach(function(rootNode) {
                html += renderTreeNode(rootNode);
            });
            
            treeDiv.html(html);
            treeDiv.show();
        }
        
        function renderAgentNode(agent, isTree) {
            var level = agent.agentLevel || 0;
            var childrenCount = agent.children ? agent.children.length : 0;
            
            return '<div class="tree-node level-' + level + '">' +
                '<div class="agent-info">' +
                    '<div class="agent-basic">' +
                        '<div class="agent-name">' + (agent.agentRealName || agent.agentName || '未知') + '</div>' +
                        '<div class="agent-details">ID: ' + agent.id + ' | 电话: ' + (agent.agentPhone || '未设置') + ' | 级别: ' + level + '</div>' +
                    '</div>' +
                    '<div class="agent-stats">' +
                        (isTree ? '下级: ' + childrenCount + '个' : '') +
                    '</div>' +
                '</div>' +
            '</div>';
        }
        
        function renderTreeNode(node) {
            var html = renderAgentNode(node, true);
            
            if (node.children && node.children.length > 0) {
                node.children.forEach(function(child) {
                    html += renderTreeNode(child);
                });
            }
            
            return html;
        }
    </script>
</body>
</html>
