<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户密码查询</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="number"] {
            width: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .password-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .password-item {
            margin-bottom: 10px;
            padding: 8px;
            background-color: white;
            border-left: 4px solid #007bff;
            border-radius: 0 4px 4px 0;
        }
        .password-label {
            font-weight: bold;
            color: #495057;
            margin-right: 10px;
        }
        .password-value {
            font-family: monospace;
            background-color: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            word-break: break-all;
        }
        .user-info {
            background-color: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>用户密码查询</h1>
        
        <div class="warning">
            <strong>⚠️ 安全提醒：</strong>此功能仅供管理员使用，请谨慎操作，确保信息安全。
        </div>
        
        <form id="passwordQueryForm">
            <div class="form-group">
                <label for="userId">用户ID：</label>
                <input type="number" id="userId" name="userId" placeholder="请输入用户ID" required>
                <button type="submit">查询密码</button>
            </div>
        </form>
        
        <div id="result" class="result">
            <div id="resultMessage"></div>
            <div id="passwordInfo" class="password-info" style="display: none;">
                <div class="user-info">
                    <div><strong>用户信息：</strong></div>
                    <div>用户ID：<span id="userIdDisplay"></span></div>
                    <div>手机号：<span id="phoneDisplay"></span></div>
                    <div>真实姓名：<span id="realNameDisplay"></span></div>
                </div>
                
                <div><strong>密码信息：</strong></div>
                <div class="password-item">
                    <span class="password-label">登录密码：</span>
                    <span class="password-value" id="loginPasswordDisplay"></span>
                </div>
                <div class="password-item">
                    <span class="password-label">提现密码（旧）：</span>
                    <span class="password-value" id="withdrawPasswordDisplay"></span>
                </div>
                <div class="password-item">
                    <span class="password-label">提现密码（新）：</span>
                    <span class="password-value" id="withdrawalPasswordDisplay"></span>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#passwordQueryForm').on('submit', function(e) {
                e.preventDefault();
                
                var userId = $('#userId').val();
                if (!userId) {
                    showResult('error', '请输入用户ID');
                    return;
                }
                
                // 发送查询请求
                $.ajax({
                    url: '/admin/user/getUserPasswords.do',
                    type: 'POST',
                    data: {
                        userId: userId
                    },
                    success: function(response) {
                        if (response.status === 0) {
                            // 查询成功
                            showPasswordInfo(response.data);
                            showResult('success', response.msg);
                        } else {
                            // 查询失败
                            showResult('error', response.msg);
                            hidePasswordInfo();
                        }
                    },
                    error: function(xhr, status, error) {
                        showResult('error', '请求失败：' + error);
                        hidePasswordInfo();
                    }
                });
            });
        });
        
        function showResult(type, message) {
            var resultDiv = $('#result');
            var messageDiv = $('#resultMessage');
            
            resultDiv.removeClass('success error').addClass(type);
            messageDiv.text(message);
            resultDiv.show();
        }
        
        function showPasswordInfo(data) {
            $('#userIdDisplay').text(data.userId || '');
            $('#phoneDisplay').text(data.phone || '');
            $('#realNameDisplay').text(data.realName || '');
            $('#loginPasswordDisplay').text(data.loginPassword || '未设置');
            $('#withdrawPasswordDisplay').text(data.withdrawPassword || '未设置');
            $('#withdrawalPasswordDisplay').text(data.withdrawalPassword || '未设置');
            
            $('#passwordInfo').show();
        }
        
        function hidePasswordInfo() {
            $('#passwordInfo').hide();
        }
    </script>
</body>
</html>
