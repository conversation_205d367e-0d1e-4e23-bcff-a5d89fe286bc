<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户列表（包含下级代理用户）</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .search-form {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            align-items: center;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 150px;
        }
        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group input, .form-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            height: fit-content;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
        .user-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .user-table th, .user-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .user-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .user-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .user-table tr:hover {
            background-color: #e3f2fd;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-active { background-color: #d4edda; color: #155724; }
        .status-inactive { background-color: #f8d7da; color: #721c24; }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .account-type {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .account-real { background-color: #d1ecf1; color: #0c5460; }
        .account-demo { background-color: #f0d0ff; color: #6f42c1; }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }
        .pagination button {
            padding: 8px 12px;
            background-color: #6c757d;
        }
        .pagination button:hover {
            background-color: #5a6268;
        }
        .pagination button:disabled {
            background-color: #e9ecef;
            color: #6c757d;
            cursor: not-allowed;
        }
        .pagination .current-page {
            background-color: #007bff;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .money {
            font-weight: bold;
            color: #28a745;
        }
        .agent-info {
            font-size: 0.9em;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>用户列表（包含下级代理用户）</h1>
        
        <!-- 搜索表单 -->
        <div class="search-form">
            <form id="searchForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="realName">真实姓名：</label>
                        <input type="text" id="realName" name="realName" placeholder="请输入真实姓名">
                    </div>
                    <div class="form-group">
                        <label for="phone">手机号：</label>
                        <input type="text" id="phone" name="phone" placeholder="请输入手机号">
                    </div>
                    <div class="form-group">
                        <label for="agentId">代理ID：</label>
                        <input type="number" id="agentId" name="agentId" placeholder="留空查询当前代理">
                    </div>
                    <div class="form-group">
                        <label for="accountType">账户类型：</label>
                        <select id="accountType" name="accountType">
                            <option value="">全部</option>
                            <option value="0">真实账户</option>
                            <option value="1">模拟账户</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="status">状态：</label>
                        <select id="status" name="status">
                            <option value="">全部</option>
                            <option value="0">未认证</option>
                            <option value="1">认证中</option>
                            <option value="2">已认证</option>
                            <option value="3">认证失败</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="pageSize">每页显示：</label>
                        <select id="pageSize" name="pageSize">
                            <option value="10">10条</option>
                            <option value="20">20条</option>
                            <option value="50">50条</option>
                            <option value="100">100条</option>
                        </select>
                    </div>
                    <button type="submit">查询</button>
                    <button type="button" onclick="resetForm()">重置</button>
                </div>
            </form>
        </div>
        
        <!-- 结果信息 -->
        <div id="resultInfo" class="result-info"></div>
        
        <!-- 加载状态 -->
        <div id="loading" class="loading" style="display: none;">
            正在加载用户数据...
        </div>
        
        <!-- 错误信息 -->
        <div id="error" class="error" style="display: none;"></div>
        
        <!-- 用户表格 -->
        <div id="userTableContainer" style="display: none;">
            <table class="user-table">
                <thead>
                    <tr>
                        <th>用户ID</th>
                        <th>真实姓名</th>
                        <th>手机号</th>
                        <th>代理信息</th>
                        <th>账户类型</th>
                        <th>状态</th>
                        <th>总资金</th>
                        <th>可用资金</th>
                        <th>当前仓位</th>
                        <th>总持仓</th>
                        <th>注册时间</th>
                    </tr>
                </thead>
                <tbody id="userTableBody">
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <div id="pagination" class="pagination" style="display: none;"></div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        let currentPage = 1;
        let currentPageSize = 10;
        let totalPages = 0;
        
        $(document).ready(function() {
            // 页面加载时执行一次查询
            searchUsers();
            
            $('#searchForm').on('submit', function(e) {
                e.preventDefault();
                currentPage = 1; // 重置到第一页
                searchUsers();
            });
        });
        
        function searchUsers() {
            showLoading();
            hideError();
            hideResults();
            
            const formData = {
                realName: $('#realName').val(),
                phone: $('#phone').val(),
                agentId: $('#agentId').val() || null,
                accountType: $('#accountType').val() || null,
                status: $('#status').val() || null,
                pageNum: currentPage,
                pageSize: parseInt($('#pageSize').val()) || 10
            };
            
            currentPageSize = formData.pageSize;
            
            $.ajax({
                url: '/admin/userBiz/listByAdminWithSubAgents.do',
                type: 'POST',
                data: formData,
                success: function(response) {
                    hideLoading();
                    if (response.status === 0) {
                        displayUsers(response.data);
                        showResultInfo(response.data);
                    } else {
                        showError(response.msg);
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();
                    showError('请求失败：' + error);
                }
            });
        }
        
        function displayUsers(pageInfo) {
            const users = pageInfo.list;
            const tbody = $('#userTableBody');
            tbody.empty();
            
            if (users && users.length > 0) {
                users.forEach(function(user) {
                    const row = createUserRow(user);
                    tbody.append(row);
                });
                
                $('#userTableContainer').show();
                updatePagination(pageInfo);
            } else {
                showError('没有找到符合条件的用户');
            }
        }
        
        function createUserRow(user) {
            const statusText = getStatusText(user.isActive);
            const statusClass = getStatusClass(user.isActive);
            const accountTypeText = user.accountType === 0 ? '真实账户' : '模拟账户';
            const accountTypeClass = user.accountType === 0 ? 'account-real' : 'account-demo';
            
            return `
                <tr>
                    <td>${user.id}</td>
                    <td>${user.realName || '未设置'}</td>
                    <td>${user.phone}</td>
                    <td class="agent-info">
                        ID: ${user.agentId}<br>
                        名称: ${user.agentName || '未知'}
                    </td>
                    <td><span class="account-type ${accountTypeClass}">${accountTypeText}</span></td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td class="money">¥${formatMoney(user.userAmt)}</td>
                    <td class="money">¥${formatMoney(user.enableAmt)}</td>
                    <td class="money">¥${formatMoney(user.currentPositionAmt)}</td>
                    <td class="money">¥${formatMoney(user.totalPositionAmt)}</td>
                    <td>${formatDate(user.regTime)}</td>
                </tr>
            `;
        }
        
        function getStatusText(status) {
            switch(status) {
                case 0: return '未认证';
                case 1: return '认证中';
                case 2: return '已认证';
                case 3: return '认证失败';
                default: return '未知';
            }
        }
        
        function getStatusClass(status) {
            switch(status) {
                case 0: return 'status-inactive';
                case 1: return 'status-pending';
                case 2: return 'status-active';
                case 3: return 'status-inactive';
                default: return 'status-inactive';
            }
        }
        
        function formatMoney(amount) {
            if (amount == null || amount === undefined) return '0.00';
            return parseFloat(amount).toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }
        
        function formatDate(dateStr) {
            if (!dateStr) return '未知';
            const date = new Date(dateStr);
            return date.toLocaleString('zh-CN');
        }
        
        function updatePagination(pageInfo) {
            totalPages = pageInfo.pages;
            currentPage = pageInfo.pageNum;
            
            const pagination = $('#pagination');
            pagination.empty();
            
            // 上一页按钮
            const prevBtn = $(`<button ${currentPage <= 1 ? 'disabled' : ''}>上一页</button>`);
            prevBtn.click(function() {
                if (currentPage > 1) {
                    currentPage--;
                    searchUsers();
                }
            });
            pagination.append(prevBtn);
            
            // 页码信息
            pagination.append(`<span>第 ${currentPage} 页，共 ${totalPages} 页，总计 ${pageInfo.total} 条记录</span>`);
            
            // 下一页按钮
            const nextBtn = $(`<button ${currentPage >= totalPages ? 'disabled' : ''}>下一页</button>`);
            nextBtn.click(function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    searchUsers();
                }
            });
            pagination.append(nextBtn);
            
            pagination.show();
        }
        
        function showResultInfo(pageInfo) {
            const info = `查询完成：找到 ${pageInfo.total} 个用户，当前显示第 ${pageInfo.pageNum} 页`;
            $('#resultInfo').text(info).show();
        }
        
        function showLoading() {
            $('#loading').show();
        }
        
        function hideLoading() {
            $('#loading').hide();
        }
        
        function showError(message) {
            $('#error').text(message).show();
        }
        
        function hideError() {
            $('#error').hide();
        }
        
        function hideResults() {
            $('#userTableContainer').hide();
            $('#pagination').hide();
            $('#resultInfo').hide();
        }
        
        function resetForm() {
            $('#searchForm')[0].reset();
            currentPage = 1;
            searchUsers();
        }
    </script>
</body>
</html>
