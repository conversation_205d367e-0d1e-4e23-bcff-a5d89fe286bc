<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.SiteAdminMapper" >
  <resultMap id="BaseResultMap" type="com.nq.pojo.SiteAdmin" >
    <constructor >
      <idArg column="id" jdbcType="INTEGER" javaType="java.lang.Integer" />
      <arg column="admin_name" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="admin_pwd" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="admin_phone" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="is_lock" jdbcType="INTEGER" javaType="java.lang.Integer" />
      <arg column="add_time" jdbcType="TIMESTAMP" javaType="java.util.Date" />
    </constructor>
  </resultMap>
  <sql id="Base_Column_List" >
    id, admin_name, admin_pwd, admin_phone, is_lock, add_time
  </sql>
  <select id="selectByPrimaryKey" resultType="com.nq.pojo.SiteAdmin" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from site_admin
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from site_admin
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.nq.pojo.SiteAdmin" >
    insert into site_admin (id, admin_name, admin_pwd, 
      admin_phone, is_lock, add_time
      )
    values (#{id,jdbcType=INTEGER}, #{adminName,jdbcType=VARCHAR}, #{adminPwd,jdbcType=VARCHAR},
      #{adminPhone,jdbcType=VARCHAR}, #{isLock,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.nq.pojo.SiteAdmin" >
    insert into site_admin
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="adminName != null" >
        admin_name,
      </if>
      <if test="adminPwd != null" >
        admin_pwd,
      </if>
      <if test="adminPhone != null" >
        admin_phone,
      </if>
      <if test="isLock != null" >
        is_lock,
      </if>
      <if test="addTime != null" >
        add_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="adminName != null" >
        #{adminName,jdbcType=VARCHAR},
      </if>
      <if test="adminPwd != null" >
        #{adminPwd,jdbcType=VARCHAR},
      </if>
      <if test="adminPhone != null" >
        #{adminPhone,jdbcType=VARCHAR},
      </if>
      <if test="isLock != null" >
        #{isLock,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.nq.pojo.SiteAdmin" >
    update site_admin
    <set >
      <if test="adminName != null" >
        admin_name = #{adminName,jdbcType=VARCHAR},
      </if>
      <if test="adminPwd != null" >
        admin_pwd = #{adminPwd,jdbcType=VARCHAR},
      </if>
      <if test="adminPhone != null" >
        admin_phone = #{adminPhone,jdbcType=VARCHAR},
      </if>
      <if test="isLock != null" >
        is_lock = #{isLock,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.nq.pojo.SiteAdmin" >
    update site_admin
    set admin_name = #{adminName,jdbcType=VARCHAR},
      admin_pwd = #{adminPwd,jdbcType=VARCHAR},
      admin_phone = #{adminPhone,jdbcType=VARCHAR},
      is_lock = #{isLock,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>





  <select id="login"  parameterType="map" resultType="com.nq.pojo.SiteAdmin">
    SELECT
    <include refid="Base_Column_List"/>
    FROM site_admin
    WHERE admin_phone = #{adminPhone} and admin_pwd = #{adminPwd}
  </select>


  <select id="listByAdmin" parameterType="map" resultType="com.nq.pojo.SiteAdmin">
    SELECT
    <include refid="Base_Column_List"/>
    FROM site_admin
    <where>
      <if test="adminName != null and adminName != '' ">
        and admin_name = #{adminName}
      </if>
      <if test="adminPhone != null and adminPhone != '' ">
        and admin_phone = #{adminPhone}
      </if>
      and admin_phone != #{superAdmin}
    </where>
  </select>



  <select id="findAdminByName" parameterType="string" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM site_admin
    WHERE admin_name = #{name}
  </select>


  <select id="findAdminByPhone" parameterType="string" resultType="com.nq.pojo.SiteAdmin">
    SELECT
    <include refid="Base_Column_List"/>
    FROM site_admin
    WHERE admin_phone = #{phone}
  </select>


</mapper>


