<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.UserRechargeMapper">
    <resultMap id="BaseResultMap" type="com.nq.pojo.UserRecharge">
        <constructor>
            <idArg column="id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
            <arg column="user_id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
            <arg column="nick_name" jdbcType="VARCHAR" javaType="java.lang.String"/>
            <arg column="agent_id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
            <arg column="order_sn" jdbcType="VARCHAR" javaType="java.lang.String"/>
            <arg column="pay_sn" jdbcType="VARCHAR" javaType="java.lang.String"/>
            <arg column="pay_channel" jdbcType="VARCHAR" javaType="java.lang.String"/>
            <arg column="pay_amt" jdbcType="DECIMAL" javaType="java.math.BigDecimal"/>
            <arg column="order_status" jdbcType="INTEGER" javaType="java.lang.Integer"/>
            <arg column="order_desc" jdbcType="VARCHAR" javaType="java.lang.String"/>
            <arg column="add_time" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
            <arg column="pay_time" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
            <arg column="pay_id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
            <arg column="phone" jdbcType="VARCHAR" javaType="java.lang.String"/>
            <arg column="operator" jdbcType="VARCHAR" javaType="java.lang.String"/>
        </constructor>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , user_id, nick_name, agent_id, order_sn, pay_sn, pay_channel, pay_amt, order_status,
    order_desc, add_time, pay_time, pay_id,remark,phone,operator
    </sql>
    <sql id="Base_Column_List_with_r">
        r
        .
        id
        , r.user_id, r.nick_name, r.agent_id, r.order_sn, r.pay_sn, r.pay_channel, r.pay_amt, r.order_status,
    r.order_desc, r.add_time, r.pay_time, r.pay_id,r.remark,r.phone,r.operator
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from user_recharge
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from user_recharge
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.nq.pojo.UserRecharge">
        insert into user_recharge (id, user_id, nick_name,
                                   agent_id, order_sn, pay_sn,
                                   pay_channel, pay_amt, order_status,
                                   order_desc, add_time, pay_time, pay_id, remark, phone, operator)
        values (#{id,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{nickName,jdbcType=VARCHAR},
                #{agentId,jdbcType=INTEGER}, #{orderSn,jdbcType=VARCHAR}, #{paySn,jdbcType=VARCHAR},
                #{payChannel,jdbcType=VARCHAR}, #{payAmt,jdbcType=DECIMAL}, #{orderStatus,jdbcType=INTEGER},
                #{orderDesc,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{payTime,jdbcType=TIMESTAMP},
                #{payId,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{phone}, #{operator})
    </insert>
    <insert id="insertSelective" parameterType="com.nq.pojo.UserRecharge">
        insert into user_recharge
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="nickName != null">
                nick_name,
            </if>
            <if test="agentId != null">
                agent_id,
            </if>
            <if test="orderSn != null">
                order_sn,
            </if>
            <if test="paySn != null">
                pay_sn,
            </if>
            <if test="payChannel != null">
                pay_channel,
            </if>
            <if test="payAmt != null">
                pay_amt,
            </if>
            <if test="orderStatus != null">
                order_status,
            </if>
            <if test="orderDesc != null">
                order_desc,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="payTime != null">
                pay_time,
            </if>
            <if test="payId != null">
                pay_id,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="operator != null">
                operator,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="nickName != null">
                #{nickName,jdbcType=VARCHAR},
            </if>
            <if test="agentId != null">
                #{agentId,jdbcType=INTEGER},
            </if>
            <if test="orderSn != null">
                #{orderSn,jdbcType=VARCHAR},
            </if>
            <if test="paySn != null">
                #{paySn,jdbcType=VARCHAR},
            </if>
            <if test="payChannel != null">
                #{payChannel,jdbcType=VARCHAR},
            </if>
            <if test="payAmt != null">
                #{payAmt,jdbcType=DECIMAL},
            </if>
            <if test="orderStatus != null">
                #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="orderDesc != null">
                #{orderDesc,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payTime != null">
                #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payId != null">
                #{payId,jdbcType=INTEGER},
            </if>
            <if test="phone != null">
                #{phone},
            </if>
            <if test="operator != null">
                #{operator},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.nq.pojo.UserRecharge">
        update user_recharge
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="nickName != null">
                nick_name = #{nickName,jdbcType=VARCHAR},
            </if>
            <if test="agentId != null">
                agent_id = #{agentId,jdbcType=INTEGER},
            </if>
            <if test="orderSn != null">
                order_sn = #{orderSn,jdbcType=VARCHAR},
            </if>
            <if test="paySn != null">
                pay_sn = #{paySn,jdbcType=VARCHAR},
            </if>
            <if test="payChannel != null">
                pay_channel = #{payChannel,jdbcType=VARCHAR},
            </if>
            <if test="payAmt != null">
                pay_amt = #{payAmt,jdbcType=DECIMAL},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="orderDesc != null">
                order_desc = #{orderDesc,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="operator != null">
                operator = #{operator},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.nq.pojo.UserRecharge">
        update user_recharge
        set user_id      = #{userId,jdbcType=INTEGER},
            nick_name    = #{nickName,jdbcType=VARCHAR},
            agent_id     = #{agentId,jdbcType=INTEGER},
            order_sn     = #{orderSn,jdbcType=VARCHAR},
            pay_sn       = #{paySn,jdbcType=VARCHAR},
            pay_channel  = #{payChannel,jdbcType=VARCHAR},
            pay_amt      = #{payAmt,jdbcType=DECIMAL},
            order_status = #{orderStatus,jdbcType=INTEGER},
            order_desc   = #{orderDesc,jdbcType=VARCHAR},
            add_time     = #{addTime,jdbcType=TIMESTAMP},
            pay_time     = #{payTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>


    <select id="checkInMoney" resultType="integer" parameterType="map">
        SELECT COUNT(*)
        FROM user_recharge
        WHERE user_id = #{userId}
          and order_status = #{status}
          and add_time > date_sub(now(), interval 1 hour)
    </select>


    <select id="findUserRechargeByOrderSn" parameterType="string" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_recharge
        WHERE order_sn = #{orderSn}
    </select>


    <select id="findUserChargeList" resultMap="BaseResultMap" parameterType="map">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_recharge
        <where>
            user_id = #{uid}
            <if test="payChannel != null and payChannel != '' ">
                and pay_channel = #{payChannel}
            </if>
            <if test="orderStatus != null and orderStatus != '' ">
                and order_status = #{orderStatus}
            </if>
        </where>
        ORDER BY id DESC
    </select>


    <select id="listByAdmin" parameterType="map" resultType="com.nq.pojo.UserRecharge">
        SELECT
        <include refid="Base_Column_List_with_r"/>
        FROM user_recharge r,`user` u
        where r.agent_id != 1
        <if test="agentId != null">
            and ( r.agent_id = #{agentId}
            or r.agent_id in (
            select agent_user.id
            FROM agent_user
            where parent_id = ${agentId}
            )
            )
        </if>
        <if test="userId != null">
            and r.user_id = #{userId}
        </if>
        <if test="realName != null and realName != '' ">
            and r.nick_name like CONCAT('%','${realName}','%')
        </if>
        <if test="state != null">
            and r.order_status = #{state}
        </if>

        <if test="begin_time != null ">
            and r.add_time <![CDATA[>=]]> #{begin_time}
        </if>
        <if test="end_time != null ">
            and r.add_time <![CDATA[<=]]> #{end_time}
        </if>
        <if test="accountType !=null">
            and u.account_type = #{accountType}
        </if>
        <if test="phone !=null and phone !='' ">
            and r.phone like CONCAT('%','${phone}','%')
        </if>
        <if test="orderNo != null and orderNo != '' ">
            and r.order_sn like CONCAT('%','${orderNo}','%')
        </if>
        and u.id = r.user_id
        ORDER BY r.id DESC
    </select>


    <delete id="deleteByUserId" parameterType="integer">
        DELETE
        FROM user_recharge
        WHERE user_id = #{userId}
    </delete>


    <select id="listByAgent" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_recharge
        <where>
            agent_id = #{searchId}
            <if test="realName != null and realName != '' ">
                and nick_name like CONCAT('%','${realName}','%')
            </if>
            <if test="payChannel != null and payChannel != '' ">
                and pay_channel = #{payChannel}
            </if>
            <if test="state != null">
                and order_status = #{state}
            </if>
            <if test="phone !=null">
                and phone = #{phone}
            </if>
        </where>
        ORDER BY id DESC
    </select>


    <!--累计充值金额 统计所有真实用户数据-->
    <select id="CountChargeSumAmt" resultType="decimal" parameterType="map">
        SELECT sum(r.pay_amt)
        FROM user_recharge r,
        `user` u
        WHERE r.order_status = 1
        and u.id = r.user_id
        <if test="accountType != null">
            and u.account_type = #{accountType}
        </if>
        <if test="agentId != null">
            and u.agent_id = #{agentId}
        </if>

    </select>

    <!--今日充值金额 统计所有真实用户-->
    <select id="CountTotalRechargeAmountByTime" parameterType="map" resultType="decimal">
        select sum(IFNULL(r.pay_amt, 0)) pay_amt
        FROM user_recharge r,
        `user` u
        where r.order_status = 1
        and TO_DAYS(r.pay_time) = TO_DAYS(NOW())
        and u.id = r.user_id
        <if test="accountType != null">
            and u.account_type = #{accountType}
        </if>
        <if test="agentId != null">
            and u.agent_id = #{agentId}
        </if>
    </select>

    <!--用户累计充值金额 -->
    <select id="getUserTotalRechargeAmountByStatus" resultType="decimal" parameterType="map">
        SELECT sum(r.pay_amt)
        FROM user_recharge r
        WHERE r.order_status = #{status}
          and r.user_id = #{userId}
    </select>

    <!--    获取今日首充记录-->
    <select id="getTodayFirstRecharge" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_recharge
        WHERE user_id in
        (select user_id from user_recharge WHERE order_status=1 GROUP BY user_id HAVING count(*) = 1
        )
        and DATE(add_time) = CURDATE();
    </select>

    <!-- 添加统计用户当日充值次数的SQL -->
    <select id="countUserDailyRecharges" resultType="int" parameterType="java.lang.Integer">
        SELECT COUNT(1)
        FROM user_recharge
        WHERE user_id = #{userId}
          AND DATE (add_time) = CURDATE()
    </select>

</mapper>